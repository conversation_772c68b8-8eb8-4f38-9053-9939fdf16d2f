#!/usr/bin/env python3
"""
Investigate CIDB API endpoints
"""

import requests
from bs4 import BeautifulSoup
import re
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def investigate_cidb_website():
    """Investigate the CIDB website to find the correct API endpoints"""
    
    base_url = "https://portal.cidb.org.za/RegisterOfContractors/"
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate, br",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1"
    }
    
    try:
        logger.info(f"Fetching main page: {base_url}")
        response = requests.get(base_url, headers=headers, timeout=30)
        response.raise_for_status()
        
        logger.info(f"Response status: {response.status_code}")
        logger.info(f"Response headers: {dict(response.headers)}")
        
        # Parse the HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Look for forms and their action URLs
        forms = soup.find_all('form')
        logger.info(f"Found {len(forms)} forms on the page")
        
        for i, form in enumerate(forms):
            action = form.get('action', 'No action')
            method = form.get('method', 'GET')
            logger.info(f"Form {i+1}: Action='{action}', Method='{method}'")
        
        # Look for JavaScript files that might contain API endpoints
        scripts = soup.find_all('script', src=True)
        logger.info(f"Found {len(scripts)} external scripts")
        
        for script in scripts:
            src = script.get('src')
            if src:
                logger.info(f"Script: {src}")
        
        # Look for inline JavaScript that might contain API calls
        inline_scripts = soup.find_all('script', src=False)
        logger.info(f"Found {len(inline_scripts)} inline scripts")
        
        # Search for potential API endpoints in the HTML
        api_patterns = [
            r'GetContractorByCriteria',
            r'/api/',
            r'\.ashx',
            r'\.asmx',
            r'ajax',
            r'DataTables'
        ]
        
        html_content = response.text
        for pattern in api_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            if matches:
                logger.info(f"Found pattern '{pattern}': {len(matches)} matches")
        
        # Look for DataTables configuration
        datatable_match = re.search(r'DataTable\s*\(\s*{([^}]+)}\s*\)', html_content, re.IGNORECASE | re.DOTALL)
        if datatable_match:
            logger.info("Found DataTable configuration:")
            logger.info(datatable_match.group(1))
        
        # Save the HTML for manual inspection
        with open('cidb_main_page.html', 'w', encoding='utf-8') as f:
            f.write(response.text)
        logger.info("Saved main page HTML to 'cidb_main_page.html'")
        
        return response.text
        
    except requests.RequestException as e:
        logger.error(f"Error fetching main page: {e}")
        return None

def test_potential_endpoints():
    """Test various potential API endpoints"""
    
    base_url = "https://portal.cidb.org.za"
    potential_endpoints = [
        "/RegisterOfContractors/GetContractorByCriteria",
        "/RegisterOfContractors/GetContractors",
        "/RegisterOfContractors/Search",
        "/RegisterOfContractors/Data",
        "/api/contractors",
        "/handlers/contractors.ashx",
        "/services/contractors.asmx"
    ]
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "X-Requested-With": "XMLHttpRequest",
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"
    }
    
    for endpoint in potential_endpoints:
        url = base_url + endpoint
        logger.info(f"Testing endpoint: {url}")
        
        try:
            # Test GET request
            response = requests.get(url, headers=headers, timeout=10)
            logger.info(f"GET {url}: Status {response.status_code}")
            
            # Test POST request with minimal data
            post_data = {
                "draw": 1,
                "start": 0,
                "length": 10,
                "IsActive": "true"
            }
            
            response = requests.post(url, data=post_data, headers=headers, timeout=10)
            logger.info(f"POST {url}: Status {response.status_code}")
            
            if response.status_code == 200:
                logger.info(f"SUCCESS! Found working endpoint: {url}")
                logger.info(f"Response content preview: {response.text[:200]}...")
                
        except requests.RequestException as e:
            logger.info(f"Error testing {url}: {e}")

if __name__ == "__main__":
    logger.info("Starting CIDB API investigation...")
    
    # First, get the main page
    html_content = investigate_cidb_website()
    
    # Then test potential endpoints
    logger.info("\n" + "="*50)
    logger.info("Testing potential API endpoints...")
    test_potential_endpoints()
