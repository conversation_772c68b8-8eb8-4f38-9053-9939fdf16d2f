#!/usr/bin/env python3
"""
Test script to verify CIDB scraper configuration
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("🔧 CIDB Scraper Configuration Test")
print("=" * 50)

# Check required environment variables
required_vars = {
    'SUPABASE_URL': 'Supabase database URL',
    'SUPABASE_KEY': 'Supabase API key',
    'FIRECRAWL_API_KEY': 'FireCrawl API key (optional)',
    'OPENAI_API_KEY': 'OpenAI API key (optional)',
    'ANTHROPIC_API_KEY': 'Anthropic API key (optional)'
}

config_status = {}
missing_required = []

for var, description in required_vars.items():
    value = os.getenv(var)
    if value:
        masked_value = value[:8] + '...' if len(value) > 10 else 'SET'
        config_status[var] = f"✅ {masked_value}"
    else:
        config_status[var] = "❌ NOT SET"
        if var in ['SUPABASE_URL', 'SUPABASE_KEY']:
            missing_required.append(var)

print("\n📋 Environment Variables:")
for var, status in config_status.items():
    print(f"  {var}: {status}")

# Check Python dependencies
print("\n📦 Python Dependencies:")
try:
    import crawl4ai
    print("  ✅ crawl4ai installed")
except ImportError:
    print("  ❌ crawl4ai NOT installed")

try:
    import firecrawl
    print("  ✅ firecrawl-py installed")
except ImportError:
    print("  ❌ firecrawl-py NOT installed")

try:
    import supabase
    print("  ✅ supabase installed")
except ImportError:
    print("  ❌ supabase NOT installed")

try:
    import openai
    print("  ✅ openai installed")
except ImportError:
    print("  ❌ openai NOT installed")

try:
    import anthropic
    print("  ✅ anthropic installed")
except ImportError:
    print("  ❌ anthropic NOT installed")

# Check system dependencies
print("\n🖥️  System Dependencies:")
try:
    import subprocess
    result = subprocess.run(['tesseract', '--version'], capture_output=True, text=True)
    if result.returncode == 0:
        print("  ✅ Tesseract OCR installed")
    else:
        print("  ❌ Tesseract OCR NOT installed (optional for screenshot analysis)")
except:
    print("  ❌ Tesseract OCR NOT installed (optional for screenshot analysis)")

# Summary
print("\n" + "=" * 50)
if missing_required:
    print("❌ Missing required configuration:")
    for var in missing_required:
        print(f"   - {var}: {required_vars[var]}")
    print("\n💡 To fix: Copy .env.example to .env and add your API keys")
else:
    print("✅ All required configuration found!")
    print("\n🚀 Ready to run the scraper!")
    
    # Check for at least one LLM API key
    has_llm = any(os.getenv(key) for key in ['OPENAI_API_KEY', 'ANTHROPIC_API_KEY'])
    if not has_llm:
        print("\n⚠️  Warning: No LLM API keys found. LLM extraction will not work.")
        print("   Add either OPENAI_API_KEY or ANTHROPIC_API_KEY for AI-powered extraction.")