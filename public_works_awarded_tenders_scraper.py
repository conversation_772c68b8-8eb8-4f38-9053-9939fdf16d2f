#!/usr/bin/env python3
"""
Public Works Awarded Tenders Comprehensive Scraper
Scrapes all awarded tenders, archives, awards, and registers from Public Works website
"""

import asyncio
import json
import os
import uuid
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging
from dataclasses import dataclass
import re

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
from supabase import create_client, Client
from dotenv import load_dotenv
import pandas as pd

# Load environment variables
load_dotenv()

# Configuration
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')
PUBLIC_WORKS_BASE_URL = "http://www.publicworks.gov.za"

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('public_works_awarded_tenders_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ScrapingStats:
    """Track scraping statistics"""
    run_id: str
    total_awarded_tenders: int = 0
    total_archived_tenders: int = 0
    total_registers: int = 0
    new_records: int = 0
    updated_records: int = 0
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []

class PublicWorksAwardedTendersScraper:
    """Comprehensive Public Works Awarded Tenders Scraper"""
    
    def __init__(self):
        self.stats = ScrapingStats(run_id=str(uuid.uuid4()))
        self.browser_config = BrowserConfig(
            headless=True,
            verbose=False,
            java_script_enabled=True,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        )
        
        # URLs to scrape
        self.urls_to_scrape = {
            'current_awarded': f"{PUBLIC_WORKS_BASE_URL}/tenders.html",
            'archived_awards': [
                f"{PUBLIC_WORKS_BASE_URL}/awardsarc2024-25.html",
                f"{PUBLIC_WORKS_BASE_URL}/awardsarc2023-24.html", 
                f"{PUBLIC_WORKS_BASE_URL}/awardsarc2022-23.html",
                f"{PUBLIC_WORKS_BASE_URL}/awardsarc2021-22.html",
                f"{PUBLIC_WORKS_BASE_URL}/awardsarc2020-21.html",
                f"{PUBLIC_WORKS_BASE_URL}/awardsarc2019-20.html",
                f"{PUBLIC_WORKS_BASE_URL}/awardsarc2018-19.html",
                f"{PUBLIC_WORKS_BASE_URL}/awardsarc2017-18.html",
                f"{PUBLIC_WORKS_BASE_URL}/awardsarc2016-17.html"
            ],
            'archived_tenders': [
                f"{PUBLIC_WORKS_BASE_URL}/tendersarch2024-25.html",
                f"{PUBLIC_WORKS_BASE_URL}/tendersarch2023-24.html",
                f"{PUBLIC_WORKS_BASE_URL}/tendersarch2022-23.html",
                f"{PUBLIC_WORKS_BASE_URL}/tendersarch2021-22.html",
                f"{PUBLIC_WORKS_BASE_URL}/tendersarch2020-21.html",
                f"{PUBLIC_WORKS_BASE_URL}/tendersarch2019-20.html",
                f"{PUBLIC_WORKS_BASE_URL}/tendersarch2018-19.html",
                f"{PUBLIC_WORKS_BASE_URL}/tendersarch2017-18.html"
            ],
            'archived_registers': [
                f"{PUBLIC_WORKS_BASE_URL}/regarch2024-25.html",
                f"{PUBLIC_WORKS_BASE_URL}/regarch2023-24.html",
                f"{PUBLIC_WORKS_BASE_URL}/regarch2022-23.html",
                f"{PUBLIC_WORKS_BASE_URL}/regarch2021-22.html",
                f"{PUBLIC_WORKS_BASE_URL}/regarch2020-21.html",
                f"{PUBLIC_WORKS_BASE_URL}/regarch2019-20.html",
                f"{PUBLIC_WORKS_BASE_URL}/regarch2018-19.html",
                f"{PUBLIC_WORKS_BASE_URL}/regarch2017-18.html"
            ]
        }
    
    async def create_database_tables(self):
        """Create database tables for awarded tenders data"""
        try:
            logger.info("Creating awarded tenders database tables...")
            
            # Create awarded_tenders table
            create_awarded_tenders_sql = """
            CREATE TABLE IF NOT EXISTS awarded_tenders (
                id SERIAL PRIMARY KEY,
                bid_number TEXT UNIQUE NOT NULL,
                date_awarded DATE,
                service_description TEXT,
                contractor_name TEXT,
                contract_value DECIMAL,
                contract_value_text TEXT,
                bbee_level TEXT,
                data_source TEXT DEFAULT 'current',
                archive_year TEXT,
                scraped_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW(),
                run_id TEXT,
                
                -- Additional metadata
                created_at TIMESTAMPTZ DEFAULT NOW()
            );
            """
            
            # Create archived_tenders table
            create_archived_tenders_sql = """
            CREATE TABLE IF NOT EXISTS archived_tenders (
                id SERIAL PRIMARY KEY,
                bid_number TEXT,
                closing_date DATE,
                posted_date DATE,
                service_description TEXT,
                status TEXT,
                archive_year TEXT,
                data_source TEXT DEFAULT 'archived',
                scraped_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW(),
                run_id TEXT,
                
                -- Additional metadata
                created_at TIMESTAMPTZ DEFAULT NOW(),
                
                -- Composite unique constraint
                UNIQUE(bid_number, archive_year, data_source)
            );
            """
            
            # Create bid_registers table
            create_registers_sql = """
            CREATE TABLE IF NOT EXISTS bid_registers (
                id SERIAL PRIMARY KEY,
                bid_number TEXT,
                closing_date DATE,
                posted_date DATE,
                service_description TEXT,
                archive_year TEXT,
                data_source TEXT DEFAULT 'register',
                scraped_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW(),
                run_id TEXT,
                
                -- Additional metadata
                created_at TIMESTAMPTZ DEFAULT NOW(),
                
                -- Composite unique constraint
                UNIQUE(bid_number, archive_year, data_source)
            );
            """
            
            # Create indexes
            create_indexes_sql = """
            -- Awarded tenders indexes
            CREATE INDEX IF NOT EXISTS idx_awarded_tenders_bid_number ON awarded_tenders(bid_number);
            CREATE INDEX IF NOT EXISTS idx_awarded_tenders_date_awarded ON awarded_tenders(date_awarded);
            CREATE INDEX IF NOT EXISTS idx_awarded_tenders_contractor ON awarded_tenders(contractor_name);
            CREATE INDEX IF NOT EXISTS idx_awarded_tenders_scraped_at ON awarded_tenders(scraped_at);
            
            -- Archived tenders indexes
            CREATE INDEX IF NOT EXISTS idx_archived_tenders_bid_number ON archived_tenders(bid_number);
            CREATE INDEX IF NOT EXISTS idx_archived_tenders_archive_year ON archived_tenders(archive_year);
            CREATE INDEX IF NOT EXISTS idx_archived_tenders_closing_date ON archived_tenders(closing_date);
            
            -- Bid registers indexes
            CREATE INDEX IF NOT EXISTS idx_bid_registers_bid_number ON bid_registers(bid_number);
            CREATE INDEX IF NOT EXISTS idx_bid_registers_archive_year ON bid_registers(archive_year);
            CREATE INDEX IF NOT EXISTS idx_bid_registers_closing_date ON bid_registers(closing_date);
            """
            
            # Execute SQL commands
            sql_commands = [
                create_awarded_tenders_sql,
                create_archived_tenders_sql, 
                create_registers_sql,
                create_indexes_sql
            ]
            
            for sql in sql_commands:
                try:
                    # Note: For production, these should be run in Supabase SQL editor
                    logger.info("SQL command prepared for execution")
                except Exception as e:
                    logger.error(f"Error with SQL command: {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error creating database tables: {str(e)}")
            return False
    
    def parse_contract_value(self, value_text: str) -> tuple:
        """Parse contract value from text"""
        try:
            if not value_text:
                return None, None
            
            # Clean the text
            clean_text = value_text.strip()
            
            # Extract numeric value using regex
            value_match = re.search(r'R\s*([\d\s,\.]+)', clean_text)
            if value_match:
                value_str = value_match.group(1).replace(' ', '').replace(',', '')
                try:
                    numeric_value = float(value_str)
                    return numeric_value, clean_text
                except ValueError:
                    return None, clean_text
            
            return None, clean_text
            
        except Exception as e:
            logger.warning(f"Error parsing contract value '{value_text}': {e}")
            return None, value_text
    
    def extract_bbee_level(self, contractor_text: str) -> tuple:
        """Extract B-BBEE level from contractor text"""
        try:
            if not contractor_text:
                return contractor_text, None
            
            # Look for B-BBEE level pattern
            bbee_match = re.search(r'\*\*B-BBEE level (\d+)\*\*', contractor_text)
            if bbee_match:
                bbee_level = bbee_match.group(1)
                contractor_name = contractor_text.replace(bbee_match.group(0), '').strip()
                return contractor_name, bbee_level
            
            return contractor_text, None
            
        except Exception as e:
            logger.warning(f"Error extracting B-BBEE level: {e}")
            return contractor_text, None
    
    def parse_date(self, date_str: str) -> Optional[datetime]:
        """Parse date string to datetime object"""
        try:
            if not date_str or date_str.strip() == "":
                return None
            
            # Clean the string
            date_str = date_str.strip()
            
            # Common date patterns
            patterns = [
                r'(\d{1,2})\s+(\w+)\s+(\d{4})',  # "09 Jul 2025"
                r'(\d{1,2})/(\d{1,2})/(\d{4})',  # "09/07/2025"
                r'(\d{4})-(\d{1,2})-(\d{1,2})',  # "2025-07-09"
            ]
            
            month_map = {
                'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
                'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12,
                'January': 1, 'February': 2, 'March': 3, 'April': 4, 'May': 5, 'June': 6,
                'July': 7, 'August': 8, 'September': 9, 'October': 10, 'November': 11, 'December': 12
            }
            
            for pattern in patterns:
                match = re.search(pattern, date_str, re.IGNORECASE)
                if match:
                    if len(match.groups()) == 3:
                        if pattern.startswith(r'(\d{4})'):  # YYYY-MM-DD format
                            year = int(match.group(1))
                            month = int(match.group(2))
                            day = int(match.group(3))
                        elif '/' in pattern:  # DD/MM/YYYY format
                            day = int(match.group(1))
                            month = int(match.group(2))
                            year = int(match.group(3))
                        else:  # DD MMM YYYY format
                            day = int(match.group(1))
                            month_str = match.group(2)
                            year = int(match.group(3))
                            month = month_map.get(month_str, month_map.get(month_str.lower()))
                            if not month:
                                continue
                        
                        return datetime(year, month, day)
            
            logger.warning(f"Could not parse date: {date_str}")
            return None
            
        except Exception as e:
            logger.warning(f"Error parsing date '{date_str}': {e}")
            return None

    async def scrape_current_awarded_tenders(self, crawler: AsyncWebCrawler) -> List[Dict]:
        """Scrape current awarded tenders from main tenders page"""
        try:
            logger.info("Scraping current awarded tenders...")

            # First, get the main tenders page and click on "Awarded Bids" tab
            config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                wait_for="css:table",
                js_code=[
                    "document.querySelector('[href=\"#pills-awarded\"]')?.click();",
                    "await new Promise(resolve => setTimeout(resolve, 2000));"
                ]
            )

            result = await crawler.arun(url=self.urls_to_scrape['current_awarded'], config=config)

            if not result.success:
                logger.error(f"Failed to load awarded tenders page: {result.error_message}")
                return []

            # Extract awarded tenders data using CSS selectors
            extraction_schema = {
                "name": "Awarded Tenders",
                "baseSelector": "#pills-awarded table tbody tr",
                "fields": [
                    {"name": "bid_number", "selector": "td:nth-child(1)", "type": "text"},
                    {"name": "date_awarded", "selector": "td:nth-child(2)", "type": "text"},
                    {"name": "service_description", "selector": "td:nth-child(3)", "type": "text"},
                    {"name": "contractor", "selector": "td:nth-child(4)", "type": "text"}
                ]
            }

            extraction_strategy = JsonCssExtractionStrategy(extraction_schema, verbose=False)

            config = CrawlerRunConfig(
                extraction_strategy=extraction_strategy,
                cache_mode=CacheMode.BYPASS,
                js_code=[
                    "document.querySelector('[href=\"#pills-awarded\"]')?.click();",
                    "await new Promise(resolve => setTimeout(resolve, 3000));"
                ]
            )

            result = await crawler.arun(url=self.urls_to_scrape['current_awarded'], config=config)

            if result.success and result.extracted_content:
                awarded_tenders = json.loads(result.extracted_content)

                # Process and clean the data
                processed_tenders = []
                for tender in awarded_tenders:
                    if tender.get('bid_number') and tender.get('bid_number').strip():
                        # Parse contract value and B-BBEE level
                        service_desc = tender.get('service_description', '')
                        contract_value, contract_value_text = self.parse_contract_value(service_desc)

                        contractor_text = tender.get('contractor', '')
                        contractor_name, bbee_level = self.extract_bbee_level(contractor_text)

                        # Parse date
                        date_awarded = self.parse_date(tender.get('date_awarded', ''))

                        processed_tender = {
                            'bid_number': tender.get('bid_number', '').strip(),
                            'date_awarded': date_awarded.date() if date_awarded else None,
                            'service_description': service_desc,
                            'contractor_name': contractor_name,
                            'contract_value': contract_value,
                            'contract_value_text': contract_value_text,
                            'bbee_level': bbee_level,
                            'data_source': 'current',
                            'archive_year': str(datetime.now().year),
                            'scraped_at': datetime.utcnow().isoformat() + "Z",
                            'run_id': self.stats.run_id
                        }

                        processed_tenders.append(processed_tender)

                logger.info(f"Scraped {len(processed_tenders)} current awarded tenders")
                return processed_tenders

            return []

        except Exception as e:
            logger.error(f"Error scraping current awarded tenders: {str(e)}")
            self.stats.errors.append(f"Current awarded tenders error: {str(e)}")
            return []

    async def scrape_archived_awards(self, crawler: AsyncWebCrawler) -> List[Dict]:
        """Scrape archived awards from all archive pages"""
        try:
            logger.info("Scraping archived awards...")
            all_archived_awards = []

            for archive_url in self.urls_to_scrape['archived_awards']:
                try:
                    # Extract year from URL
                    year_match = re.search(r'awardsarc(\d{4}-\d{2})\.html', archive_url)
                    archive_year = year_match.group(1) if year_match else 'unknown'

                    logger.info(f"Scraping archived awards for {archive_year}...")

                    # Define extraction schema for archived awards
                    extraction_schema = {
                        "name": "Archived Awards",
                        "baseSelector": "table tbody tr",
                        "fields": [
                            {"name": "bid_number", "selector": "td:nth-child(1)", "type": "text"},
                            {"name": "date_awarded", "selector": "td:nth-child(2)", "type": "text"},
                            {"name": "service_description", "selector": "td:nth-child(3)", "type": "text"},
                            {"name": "contractor", "selector": "td:nth-child(4)", "type": "text"}
                        ]
                    }

                    extraction_strategy = JsonCssExtractionStrategy(extraction_schema, verbose=False)

                    config = CrawlerRunConfig(
                        extraction_strategy=extraction_strategy,
                        cache_mode=CacheMode.BYPASS,
                        wait_for="css:table tbody tr"
                    )

                    result = await crawler.arun(url=archive_url, config=config)

                    if result.success and result.extracted_content:
                        archived_awards = json.loads(result.extracted_content)

                        # Process archived awards
                        for award in archived_awards:
                            if award.get('bid_number') and award.get('bid_number').strip():
                                # Parse contract value and B-BBEE level
                                service_desc = award.get('service_description', '')
                                contract_value, contract_value_text = self.parse_contract_value(service_desc)

                                contractor_text = award.get('contractor', '')
                                contractor_name, bbee_level = self.extract_bbee_level(contractor_text)

                                # Parse date
                                date_awarded = self.parse_date(award.get('date_awarded', ''))

                                processed_award = {
                                    'bid_number': award.get('bid_number', '').strip(),
                                    'date_awarded': date_awarded.date() if date_awarded else None,
                                    'service_description': service_desc,
                                    'contractor_name': contractor_name,
                                    'contract_value': contract_value,
                                    'contract_value_text': contract_value_text,
                                    'bbee_level': bbee_level,
                                    'data_source': 'archived_awards',
                                    'archive_year': archive_year,
                                    'scraped_at': datetime.utcnow().isoformat() + "Z",
                                    'run_id': self.stats.run_id
                                }

                                all_archived_awards.append(processed_award)

                        logger.info(f"Scraped {len(archived_awards)} awards from {archive_year}")

                    # Add delay between requests
                    await asyncio.sleep(1)

                except Exception as e:
                    logger.error(f"Error scraping archive {archive_url}: {e}")
                    self.stats.errors.append(f"Archive error {archive_url}: {str(e)}")

            logger.info(f"Total archived awards scraped: {len(all_archived_awards)}")
            return all_archived_awards

        except Exception as e:
            logger.error(f"Error scraping archived awards: {str(e)}")
            self.stats.errors.append(f"Archived awards error: {str(e)}")
            return []

    async def scrape_archived_tenders(self, crawler: AsyncWebCrawler) -> List[Dict]:
        """Scrape archived tenders from all archive pages"""
        try:
            logger.info("Scraping archived tenders...")
            all_archived_tenders = []

            for archive_url in self.urls_to_scrape['archived_tenders']:
                try:
                    # Extract year from URL
                    year_match = re.search(r'tendersarch(\d{4}-\d{2})\.html', archive_url)
                    archive_year = year_match.group(1) if year_match else 'unknown'

                    logger.info(f"Scraping archived tenders for {archive_year}...")

                    # Define extraction schema for archived tenders
                    extraction_schema = {
                        "name": "Archived Tenders",
                        "baseSelector": "table tbody tr",
                        "fields": [
                            {"name": "bid_number", "selector": "td:nth-child(1)", "type": "text"},
                            {"name": "closing_date", "selector": "td:nth-child(2)", "type": "text"},
                            {"name": "posted_date", "selector": "td:nth-child(3)", "type": "text"},
                            {"name": "service_description", "selector": "td:nth-child(4)", "type": "text"},
                            {"name": "status", "selector": "td:nth-child(5)", "type": "text"}
                        ]
                    }

                    extraction_strategy = JsonCssExtractionStrategy(extraction_schema, verbose=False)

                    config = CrawlerRunConfig(
                        extraction_strategy=extraction_strategy,
                        cache_mode=CacheMode.BYPASS,
                        wait_for="css:table tbody tr"
                    )

                    result = await crawler.arun(url=archive_url, config=config)

                    if result.success and result.extracted_content:
                        archived_tenders = json.loads(result.extracted_content)

                        # Process archived tenders
                        for tender in archived_tenders:
                            if tender.get('bid_number') and tender.get('bid_number').strip():
                                # Parse dates
                                closing_date = self.parse_date(tender.get('closing_date', ''))
                                posted_date = self.parse_date(tender.get('posted_date', ''))

                                processed_tender = {
                                    'bid_number': tender.get('bid_number', '').strip(),
                                    'closing_date': closing_date.date() if closing_date else None,
                                    'posted_date': posted_date.date() if posted_date else None,
                                    'service_description': tender.get('service_description', ''),
                                    'status': tender.get('status', ''),
                                    'archive_year': archive_year,
                                    'data_source': 'archived_tenders',
                                    'scraped_at': datetime.utcnow().isoformat() + "Z",
                                    'run_id': self.stats.run_id
                                }

                                all_archived_tenders.append(processed_tender)

                        logger.info(f"Scraped {len(archived_tenders)} tenders from {archive_year}")

                    # Add delay between requests
                    await asyncio.sleep(1)

                except Exception as e:
                    logger.error(f"Error scraping archive {archive_url}: {e}")
                    self.stats.errors.append(f"Archive error {archive_url}: {str(e)}")

            logger.info(f"Total archived tenders scraped: {len(all_archived_tenders)}")
            return all_archived_tenders

        except Exception as e:
            logger.error(f"Error scraping archived tenders: {str(e)}")
            self.stats.errors.append(f"Archived tenders error: {str(e)}")
            return []

    async def scrape_bid_registers(self, crawler: AsyncWebCrawler) -> List[Dict]:
        """Scrape bid registers from all archive pages"""
        try:
            logger.info("Scraping bid registers...")
            all_registers = []

            for archive_url in self.urls_to_scrape['archived_registers']:
                try:
                    # Extract year from URL
                    year_match = re.search(r'regarch(\d{4}-\d{2})\.html', archive_url)
                    archive_year = year_match.group(1) if year_match else 'unknown'

                    logger.info(f"Scraping bid registers for {archive_year}...")

                    # Define extraction schema for bid registers
                    extraction_schema = {
                        "name": "Bid Registers",
                        "baseSelector": "table tbody tr",
                        "fields": [
                            {"name": "bid_number", "selector": "td:nth-child(1)", "type": "text"},
                            {"name": "closing_date", "selector": "td:nth-child(2)", "type": "text"},
                            {"name": "posted_date", "selector": "td:nth-child(3)", "type": "text"},
                            {"name": "service_description", "selector": "td:nth-child(4)", "type": "text"}
                        ]
                    }

                    extraction_strategy = JsonCssExtractionStrategy(extraction_schema, verbose=False)

                    config = CrawlerRunConfig(
                        extraction_strategy=extraction_strategy,
                        cache_mode=CacheMode.BYPASS,
                        wait_for="css:table tbody tr"
                    )

                    result = await crawler.arun(url=archive_url, config=config)

                    if result.success and result.extracted_content:
                        registers = json.loads(result.extracted_content)

                        # Process bid registers
                        for register in registers:
                            if register.get('bid_number') and register.get('bid_number').strip():
                                # Parse dates
                                closing_date = self.parse_date(register.get('closing_date', ''))
                                posted_date = self.parse_date(register.get('posted_date', ''))

                                processed_register = {
                                    'bid_number': register.get('bid_number', '').strip(),
                                    'closing_date': closing_date.date() if closing_date else None,
                                    'posted_date': posted_date.date() if posted_date else None,
                                    'service_description': register.get('service_description', ''),
                                    'archive_year': archive_year,
                                    'data_source': 'register',
                                    'scraped_at': datetime.utcnow().isoformat() + "Z",
                                    'run_id': self.stats.run_id
                                }

                                all_registers.append(processed_register)

                        logger.info(f"Scraped {len(registers)} registers from {archive_year}")

                    # Add delay between requests
                    await asyncio.sleep(1)

                except Exception as e:
                    logger.error(f"Error scraping register {archive_url}: {e}")
                    self.stats.errors.append(f"Register error {archive_url}: {str(e)}")

            logger.info(f"Total bid registers scraped: {len(all_registers)}")
            return all_registers

        except Exception as e:
            logger.error(f"Error scraping bid registers: {str(e)}")
            self.stats.errors.append(f"Bid registers error: {str(e)}")
            return []

    async def save_awarded_tenders_to_supabase(self, awarded_tenders: List[Dict]):
        """Save awarded tenders to Supabase"""
        if not awarded_tenders:
            return

        try:
            logger.info(f"Saving {len(awarded_tenders)} awarded tenders to Supabase...")

            # Batch upsert
            batch_size = 50
            total_saved = 0

            for i in range(0, len(awarded_tenders), batch_size):
                batch = awarded_tenders[i:i+batch_size]

                try:
                    result = supabase.table('awarded_tenders').upsert(
                        batch,
                        on_conflict='bid_number'
                    ).execute()

                    if hasattr(result, 'error') and result.error:
                        logger.error(f"Supabase upsert error: {result.error}")
                    else:
                        total_saved += len(batch)
                        logger.info(f"✅ Saved awarded tenders batch {i//batch_size + 1}")

                except Exception as batch_error:
                    logger.error(f"Error saving awarded tenders batch: {batch_error}")

            self.stats.total_awarded_tenders = len(awarded_tenders)
            logger.info(f"✅ Saved {total_saved}/{len(awarded_tenders)} awarded tenders")

        except Exception as e:
            logger.error(f"Error saving awarded tenders: {str(e)}")
            self.stats.errors.append(f"Database save error: {str(e)}")

    async def save_archived_tenders_to_supabase(self, archived_tenders: List[Dict]):
        """Save archived tenders to Supabase"""
        if not archived_tenders:
            return

        try:
            logger.info(f"Saving {len(archived_tenders)} archived tenders to Supabase...")

            # Batch upsert
            batch_size = 50
            total_saved = 0

            for i in range(0, len(archived_tenders), batch_size):
                batch = archived_tenders[i:i+batch_size]

                try:
                    result = supabase.table('archived_tenders').upsert(
                        batch,
                        on_conflict='bid_number,archive_year,data_source'
                    ).execute()

                    if hasattr(result, 'error') and result.error:
                        logger.error(f"Supabase upsert error: {result.error}")
                    else:
                        total_saved += len(batch)
                        logger.info(f"✅ Saved archived tenders batch {i//batch_size + 1}")

                except Exception as batch_error:
                    logger.error(f"Error saving archived tenders batch: {batch_error}")

            self.stats.total_archived_tenders = len(archived_tenders)
            logger.info(f"✅ Saved {total_saved}/{len(archived_tenders)} archived tenders")

        except Exception as e:
            logger.error(f"Error saving archived tenders: {str(e)}")
            self.stats.errors.append(f"Database save error: {str(e)}")

    async def save_registers_to_supabase(self, registers: List[Dict]):
        """Save bid registers to Supabase"""
        if not registers:
            return

        try:
            logger.info(f"Saving {len(registers)} bid registers to Supabase...")

            # Batch upsert
            batch_size = 50
            total_saved = 0

            for i in range(0, len(registers), batch_size):
                batch = registers[i:i+batch_size]

                try:
                    result = supabase.table('bid_registers').upsert(
                        batch,
                        on_conflict='bid_number,archive_year,data_source'
                    ).execute()

                    if hasattr(result, 'error') and result.error:
                        logger.error(f"Supabase upsert error: {result.error}")
                    else:
                        total_saved += len(batch)
                        logger.info(f"✅ Saved bid registers batch {i//batch_size + 1}")

                except Exception as batch_error:
                    logger.error(f"Error saving bid registers batch: {batch_error}")

            self.stats.total_registers = len(registers)
            logger.info(f"✅ Saved {total_saved}/{len(registers)} bid registers")

        except Exception as e:
            logger.error(f"Error saving bid registers: {str(e)}")
            self.stats.errors.append(f"Database save error: {str(e)}")

    async def run_full_scrape(self):
        """Run complete scraping process for all awarded tenders data"""
        start_time = datetime.utcnow()

        try:
            # Initialize database
            await self.create_database_tables()

            logger.info("🚀 Starting Public Works awarded tenders comprehensive scraping...")

            async with AsyncWebCrawler(config=self.browser_config) as crawler:
                # 1. Scrape current awarded tenders
                current_awarded = await self.scrape_current_awarded_tenders(crawler)

                # 2. Scrape archived awards
                archived_awards = await self.scrape_archived_awards(crawler)

                # 3. Scrape archived tenders
                archived_tenders = await self.scrape_archived_tenders(crawler)

                # 4. Scrape bid registers
                bid_registers = await self.scrape_bid_registers(crawler)

                # 5. Save all data to database
                if current_awarded:
                    await self.save_awarded_tenders_to_supabase(current_awarded)

                if archived_awards:
                    await self.save_awarded_tenders_to_supabase(archived_awards)

                if archived_tenders:
                    await self.save_archived_tenders_to_supabase(archived_tenders)

                if bid_registers:
                    await self.save_registers_to_supabase(bid_registers)

            # Calculate final statistics
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()

            total_records = (len(current_awarded) + len(archived_awards) +
                           len(archived_tenders) + len(bid_registers))

            # Log final results
            logger.info(f"""
🎯 Comprehensive scraping completed in {duration:.2f} seconds
Run ID: {self.stats.run_id}
Current Awarded Tenders: {len(current_awarded)}
Archived Awards: {len(archived_awards)}
Archived Tenders: {len(archived_tenders)}
Bid Registers: {len(bid_registers)}
Total Records: {total_records}
Errors: {len(self.stats.errors)}
            """)

            if self.stats.errors:
                logger.warning(f"Errors encountered: {self.stats.errors}")

            return {
                'run_id': self.stats.run_id,
                'current_awarded_tenders': len(current_awarded),
                'archived_awards': len(archived_awards),
                'archived_tenders': len(archived_tenders),
                'bid_registers': len(bid_registers),
                'total_records': total_records,
                'duration_seconds': duration,
                'errors': self.stats.errors
            }

        except Exception as e:
            logger.error(f"Critical error during scraping: {str(e)}")
            raise

async def main():
    """Main entry point"""
    scraper = PublicWorksAwardedTendersScraper()

    try:
        result = await scraper.run_full_scrape()
        print(f"\n✅ Comprehensive scraping completed successfully!")
        print(f"📊 Results: {result}")
        return result

    except Exception as e:
        logger.error(f"Scraping failed: {str(e)}")
        print(f"\n❌ Scraping failed: {str(e)}")
        return None

if __name__ == "__main__":
    print("🏛️ PUBLIC WORKS AWARDED TENDERS COMPREHENSIVE SCRAPER")
    print("=" * 60)
    print("Scraping all awarded tenders, archives, awards, and registers")
    print("from: http://www.publicworks.gov.za")
    print()

    asyncio.run(main())
