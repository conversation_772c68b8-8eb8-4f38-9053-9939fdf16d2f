#!/usr/bin/env python3
"""
CIDB Screenshot Scraper
Captures screenshots of every page on the CIDB portal
Saves to Documents/cidb_screenshots folder
"""

import asyncio
import os
import time
from datetime import datetime
from pathlib import Path
import base64
from crawl4ai import As<PERSON><PERSON>eb<PERSON>raw<PERSON>, BrowserConfig, CrawlerRunConfig, CacheMode

class CIDBScreenshotScraper:
    """Specialized scraper for capturing CIDB portal screenshots"""
    
    def __init__(self):
        # Set up Documents folder path
        self.documents_path = Path.home() / "Documents"
        self.screenshots_dir = self.documents_path / "cidb_screenshots"
        
        # Create screenshots directory
        self.screenshots_dir.mkdir(exist_ok=True)
        
        print(f"📁 Screenshots will be saved to: {self.screenshots_dir}")
        
        # Browser configuration for high-quality screenshots
        self.browser_config = BrowserConfig(
            headless=True,
            verbose=True,
            java_script_enabled=True,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            viewport_width=1920,  # High resolution
            viewport_height=1080
        )
        
        # CIDB URLs to screenshot
        self.cidb_urls = [
            {
                "name": "Main_Portal",
                "url": "https://portal.cidb.org.za/RegisterOfContractors/",
                "description": "Main contractor search portal"
            },
            {
                "name": "Alternative_Portal",
                "url": "https://registers.cidb.org.za/",
                "description": "Alternative CIDB registers portal"
            },
            {
                "name": "Public_Search",
                "url": "https://registers.cidb.org.za/PublicContractors/ContractorSearch",
                "description": "Public contractor search"
            },
            {
                "name": "CIDB_Main_Site",
                "url": "https://www.cidb.org.za/",
                "description": "Main CIDB website"
            },
            {
                "name": "Resource_Centre",
                "url": "https://www.cidb.org.za/resource-centre/register-of-contractors/",
                "description": "CIDB resource centre"
            }
        ]
        
        self.screenshots_taken = 0
        self.errors = []
    
    async def capture_screenshot(self, url_info: dict, page_number: int = None) -> bool:
        """Capture a screenshot of a specific URL"""
        try:
            url = url_info["url"]
            name = url_info["name"]
            description = url_info["description"]
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if page_number:
                filename = f"{name}_Page_{page_number:02d}_{timestamp}.png"
            else:
                filename = f"{name}_{timestamp}.png"
            
            screenshot_path = self.screenshots_dir / filename
            
            print(f"📸 Capturing: {name}")
            print(f"   🔗 URL: {url}")
            print(f"   📄 Description: {description}")
            
            async with AsyncWebCrawler(config=self.browser_config) as crawler:
                config = CrawlerRunConfig(
                    cache_mode=CacheMode.BYPASS,
                    page_timeout=30000,
                    wait_for="css:body",
                    screenshot=True,
                    delay_before_return_html=3000  # Wait 3 seconds for content to load
                )
                
                result = await crawler.arun(url=url, config=config)
                
                if result.success and result.screenshot:
                    # Save screenshot
                    with open(screenshot_path, 'wb') as f:
                        f.write(base64.b64decode(result.screenshot))
                    
                    # Get file size for verification
                    file_size = screenshot_path.stat().st_size
                    
                    print(f"   ✅ Screenshot saved: {filename}")
                    print(f"   📊 File size: {file_size:,} bytes")
                    print(f"   📁 Path: {screenshot_path}")
                    
                    self.screenshots_taken += 1
                    return True
                else:
                    error_msg = f"Failed to capture {name}: {result.error_message if not result.success else 'No screenshot data'}"
                    print(f"   ❌ {error_msg}")
                    self.errors.append(error_msg)
                    return False
                    
        except Exception as e:
            error_msg = f"Error capturing {url_info['name']}: {str(e)}"
            print(f"   ❌ {error_msg}")
            self.errors.append(error_msg)
            return False
    
    async def capture_pagination_screenshots(self, base_url: str, max_pages: int = 10) -> int:
        """Capture screenshots of paginated content"""
        print(f"\n📄 CAPTURING PAGINATION SCREENSHOTS")
        print(f"   🔗 Base URL: {base_url}")
        print(f"   📊 Max pages: {max_pages}")
        print("-" * 50)
        
        pagination_screenshots = 0
        
        async with AsyncWebCrawler(config=self.browser_config) as crawler:
            for page_num in range(1, max_pages + 1):
                try:
                    print(f"\n📸 Capturing page {page_num}/{max_pages}")
                    
                    # Generate filename for this page
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"CIDB_Portal_Page_{page_num:02d}_{timestamp}.png"
                    screenshot_path = self.screenshots_dir / filename
                    
                    # Configure for this page
                    config = CrawlerRunConfig(
                        cache_mode=CacheMode.BYPASS,
                        page_timeout=30000,
                        wait_for="css:body",
                        screenshot=True,
                        delay_before_return_html=3000
                    )
                    
                    # For pages beyond 1, try to navigate to specific page
                    if page_num > 1:
                        # Try different pagination strategies
                        navigation_actions = [
                            {"type": "wait", "milliseconds": 2000},
                            {"type": "click", "selector": f"a[href*='page={page_num}'], .page-link:contains('{page_num}')"},
                            {"type": "wait", "milliseconds": 3000},
                        ]
                        
                        result = await crawler.arun(
                            url=base_url, 
                            config=config,
                            actions=navigation_actions
                        )
                    else:
                        # First page - just load normally
                        result = await crawler.arun(url=base_url, config=config)
                    
                    if result.success and result.screenshot:
                        # Save screenshot
                        with open(screenshot_path, 'wb') as f:
                            f.write(base64.b64decode(result.screenshot))
                        
                        file_size = screenshot_path.stat().st_size
                        
                        print(f"   ✅ Page {page_num} captured: {filename}")
                        print(f"   📊 File size: {file_size:,} bytes")
                        
                        pagination_screenshots += 1
                        self.screenshots_taken += 1
                    else:
                        print(f"   ❌ Failed to capture page {page_num}")
                        if page_num > 1:
                            print(f"   💡 Page {page_num} may not exist or navigation failed")
                            break  # Stop if we can't navigate to higher pages
                    
                    # Rate limiting between pages
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    print(f"   ❌ Error capturing page {page_num}: {e}")
                    self.errors.append(f"Page {page_num} error: {str(e)}")
                    continue
        
        return pagination_screenshots
    
    async def capture_all_cidb_screenshots(self):
        """Capture screenshots of all CIDB pages"""
        start_time = datetime.now()
        
        print("🚀 STARTING CIDB SCREENSHOT CAPTURE")
        print("=" * 60)
        print(f"📁 Output directory: {self.screenshots_dir}")
        print(f"🕐 Start time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # Step 1: Capture main portal pages
        print(f"\n📸 STEP 1: CAPTURING MAIN PORTAL PAGES")
        print("-" * 50)
        
        for url_info in self.cidb_urls:
            await self.capture_screenshot(url_info)
            await asyncio.sleep(2)  # Rate limiting
        
        # Step 2: Capture pagination screenshots of main portal
        print(f"\n📸 STEP 2: CAPTURING PAGINATION SCREENSHOTS")
        print("-" * 50)
        
        main_portal_url = "https://portal.cidb.org.za/RegisterOfContractors/"
        pagination_count = await self.capture_pagination_screenshots(main_portal_url, max_pages=10)
        
        # Step 3: Try different search scenarios
        print(f"\n📸 STEP 3: CAPTURING SEARCH SCENARIOS")
        print("-" * 50)
        
        search_scenarios = [
            {
                "name": "Search_Active_Contractors",
                "url": "https://portal.cidb.org.za/RegisterOfContractors/",
                "description": "Search for active contractors",
                "actions": [
                    {"type": "wait", "milliseconds": 3000},
                    {"type": "click", "selector": "select[name*='status']"},
                    {"type": "wait", "milliseconds": 1000},
                    {"type": "click", "selector": "option[value='Active']"},
                    {"type": "wait", "milliseconds": 1000},
                    {"type": "click", "selector": "button[type='submit'], .search-button"},
                    {"type": "wait", "milliseconds": 5000}
                ]
            },
            {
                "name": "Search_Gauteng_Contractors",
                "url": "https://portal.cidb.org.za/RegisterOfContractors/",
                "description": "Search for Gauteng contractors",
                "actions": [
                    {"type": "wait", "milliseconds": 3000},
                    {"type": "click", "selector": "select[name*='province']"},
                    {"type": "wait", "milliseconds": 1000},
                    {"type": "click", "selector": "option[value='Gauteng']"},
                    {"type": "wait", "milliseconds": 1000},
                    {"type": "click", "selector": "button[type='submit'], .search-button"},
                    {"type": "wait", "milliseconds": 5000}
                ]
            }
        ]
        
        async with AsyncWebCrawler(config=self.browser_config) as crawler:
            for scenario in search_scenarios:
                try:
                    print(f"\n📸 Capturing: {scenario['name']}")
                    print(f"   📄 {scenario['description']}")
                    
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"{scenario['name']}_{timestamp}.png"
                    screenshot_path = self.screenshots_dir / filename
                    
                    config = CrawlerRunConfig(
                        cache_mode=CacheMode.BYPASS,
                        page_timeout=30000,
                        wait_for="css:body",
                        screenshot=True
                    )
                    
                    result = await crawler.arun(
                        url=scenario['url'],
                        config=config,
                        actions=scenario['actions']
                    )
                    
                    if result.success and result.screenshot:
                        with open(screenshot_path, 'wb') as f:
                            f.write(base64.b64decode(result.screenshot))
                        
                        file_size = screenshot_path.stat().st_size
                        print(f"   ✅ Captured: {filename}")
                        print(f"   📊 File size: {file_size:,} bytes")
                        
                        self.screenshots_taken += 1
                    else:
                        print(f"   ❌ Failed to capture {scenario['name']}")
                    
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    print(f"   ❌ Error with {scenario['name']}: {e}")
                    self.errors.append(f"Scenario {scenario['name']}: {str(e)}")
        
        # Final summary
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"\n🎯 SCREENSHOT CAPTURE COMPLETED")
        print("=" * 60)
        print(f"📊 Total screenshots taken: {self.screenshots_taken}")
        print(f"📁 Saved to: {self.screenshots_dir}")
        print(f"⏱️ Duration: {duration:.2f} seconds")
        print(f"❌ Errors: {len(self.errors)}")
        
        if self.errors:
            print(f"\n🚨 ERRORS ENCOUNTERED:")
            for i, error in enumerate(self.errors, 1):
                print(f"   {i}. {error}")
        
        # List all captured screenshots
        screenshot_files = list(self.screenshots_dir.glob("*.png"))
        screenshot_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        print(f"\n📸 CAPTURED SCREENSHOTS:")
        print("-" * 50)
        
        total_size = 0
        for i, screenshot_file in enumerate(screenshot_files, 1):
            file_size = screenshot_file.stat().st_size
            total_size += file_size
            created_time = datetime.fromtimestamp(screenshot_file.stat().st_mtime)
            
            print(f"   {i:2d}. {screenshot_file.name}")
            print(f"       📊 Size: {file_size:,} bytes")
            print(f"       🕐 Created: {created_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print()
        
        print(f"📊 Total size: {total_size:,} bytes ({total_size/1024/1024:.2f} MB)")
        
        return {
            'screenshots_taken': self.screenshots_taken,
            'total_size_bytes': total_size,
            'duration_seconds': duration,
            'output_directory': str(self.screenshots_dir),
            'errors': self.errors,
            'files': [f.name for f in screenshot_files]
        }

async def main():
    """Main execution function"""
    scraper = CIDBScreenshotScraper()
    
    try:
        result = await scraper.capture_all_cidb_screenshots()
        
        print(f"\n🎉 MISSION ACCOMPLISHED!")
        print(f"📸 {result['screenshots_taken']} screenshots captured")
        print(f"📁 Saved to: {result['output_directory']}")
        print(f"💾 Total size: {result['total_size_bytes']/1024/1024:.2f} MB")
        
        if result['screenshots_taken'] > 0:
            print(f"\n✅ SUCCESS: All CIDB portal screenshots captured!")
            print(f"🔍 You can now view all pages visually")
            print(f"📋 Use screenshots for manual analysis")
            print(f"🖼️ High-resolution images ready for review")
        
        return result
        
    except Exception as e:
        print(f"❌ Screenshot capture failed: {e}")
        return None

if __name__ == "__main__":
    asyncio.run(main())
