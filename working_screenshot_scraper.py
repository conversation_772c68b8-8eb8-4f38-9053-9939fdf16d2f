#!/usr/bin/env python3
"""
Working CIDB Screenshot Scraper
Uses Crawl4AI to capture high-quality screenshots of CIDB portal
"""

import asyncio
import os
import time
from datetime import datetime
from pathlib import Path
import base64
from crawl4ai import Async<PERSON>ebCrawler, <PERSON><PERSON>er<PERSON>onfig, CrawlerRunConfig, CacheMode

def create_screenshots_directory():
    """Create the screenshots directory in Documents"""
    documents_path = Path.home() / "Documents"
    screenshots_dir = documents_path / "cidb_screenshots"
    screenshots_dir.mkdir(exist_ok=True)
    return screenshots_dir

async def capture_cidb_screenshots():
    """Capture screenshots of CIDB portal pages"""
    print("🚀 CIDB SCREENSHOT CAPTURE")
    print("=" * 40)
    
    # Create screenshots directory
    screenshots_dir = create_screenshots_directory()
    print(f"📁 Screenshots directory: {screenshots_dir}")
    
    # Browser configuration for high-quality screenshots
    browser_config = BrowserConfig(
        headless=True,
        verbose=True,
        java_script_enabled=True,
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        viewport_width=1920,
        viewport_height=1080
    )
    
    # URLs to capture
    urls_to_capture = [
        {
            "name": "CIDB_Main_Portal",
            "url": "https://portal.cidb.org.za/RegisterOfContractors/",
            "description": "Main CIDB contractor portal"
        },
        {
            "name": "CIDB_Alternative_Portal", 
            "url": "https://registers.cidb.org.za/",
            "description": "Alternative CIDB registers"
        },
        {
            "name": "CIDB_Public_Search",
            "url": "https://registers.cidb.org.za/PublicContractors/ContractorSearch",
            "description": "Public contractor search"
        },
        {
            "name": "CIDB_Main_Website",
            "url": "https://www.cidb.org.za/",
            "description": "Main CIDB website"
        }
    ]
    
    screenshots_captured = 0
    total_size = 0
    
    async with AsyncWebCrawler(config=browser_config) as crawler:
        # Capture main pages
        for i, url_info in enumerate(urls_to_capture, 1):
            try:
                print(f"\n📸 Capturing {i}/{len(urls_to_capture)}: {url_info['name']}")
                print(f"   🔗 URL: {url_info['url']}")
                print(f"   📄 Description: {url_info['description']}")
                
                # Configure for screenshot
                config = CrawlerRunConfig(
                    cache_mode=CacheMode.BYPASS,
                    page_timeout=30000,
                    wait_for="css:body",
                    screenshot=True,
                    delay_before_return_html=3000
                )
                
                result = await crawler.arun(url=url_info['url'], config=config)
                
                if result.success and result.screenshot:
                    # Generate filename
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"{url_info['name']}_{timestamp}.png"
                    screenshot_path = screenshots_dir / filename
                    
                    # Save screenshot
                    screenshot_data = base64.b64decode(result.screenshot)
                    with open(screenshot_path, 'wb') as f:
                        f.write(screenshot_data)
                    
                    file_size = len(screenshot_data)
                    total_size += file_size
                    
                    print(f"   ✅ Screenshot saved: {filename}")
                    print(f"   📊 File size: {file_size:,} bytes")
                    
                    screenshots_captured += 1
                else:
                    print(f"   ❌ Failed to capture {url_info['name']}")
                    if not result.success:
                        print(f"      Error: {result.error_message}")
                
                # Rate limiting
                await asyncio.sleep(2)
                
            except Exception as e:
                print(f"   ❌ Error capturing {url_info['name']}: {e}")
                continue
        
        # Capture different views of the main portal
        print(f"\n📄 CAPTURING DIFFERENT PORTAL VIEWS")
        print("-" * 40)
        
        main_portal_url = "https://portal.cidb.org.za/RegisterOfContractors/"
        
        # Different scenarios to capture
        scenarios = [
            {
                "name": "Portal_Default_View",
                "description": "Default portal view",
                "wait_time": 3000
            },
            {
                "name": "Portal_After_Wait",
                "description": "Portal after extended wait",
                "wait_time": 8000
            },
            {
                "name": "Portal_Full_Load",
                "description": "Portal with full page load",
                "wait_time": 5000
            }
        ]
        
        for scenario in scenarios:
            try:
                print(f"\n📸 Capturing: {scenario['name']}")
                print(f"   📄 {scenario['description']}")
                
                config = CrawlerRunConfig(
                    cache_mode=CacheMode.BYPASS,
                    page_timeout=30000,
                    wait_for="css:body",
                    screenshot=True,
                    delay_before_return_html=scenario['wait_time']
                )
                
                result = await crawler.arun(url=main_portal_url, config=config)
                
                if result.success and result.screenshot:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"{scenario['name']}_{timestamp}.png"
                    screenshot_path = screenshots_dir / filename
                    
                    screenshot_data = base64.b64decode(result.screenshot)
                    with open(screenshot_path, 'wb') as f:
                        f.write(screenshot_data)
                    
                    file_size = len(screenshot_data)
                    total_size += file_size
                    
                    print(f"   ✅ Screenshot saved: {filename}")
                    print(f"   📊 File size: {file_size:,} bytes")
                    
                    screenshots_captured += 1
                else:
                    print(f"   ❌ Failed to capture {scenario['name']}")
                
                await asyncio.sleep(2)
                
            except Exception as e:
                print(f"   ❌ Error capturing {scenario['name']}: {e}")
                continue
    
    # Final summary
    print(f"\n🎯 SCREENSHOT CAPTURE COMPLETED")
    print("=" * 40)
    print(f"📸 Total screenshots captured: {screenshots_captured}")
    print(f"📁 Saved to: {screenshots_dir}")
    print(f"💾 Total size: {total_size:,} bytes ({total_size/1024/1024:.2f} MB)")
    
    # List all captured files
    screenshot_files = list(screenshots_dir.glob("*.png"))
    screenshot_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
    
    if screenshot_files:
        print(f"\n📋 CAPTURED SCREENSHOTS:")
        print("-" * 30)
        
        for i, screenshot_file in enumerate(screenshot_files, 1):
            file_size = screenshot_file.stat().st_size
            created_time = datetime.fromtimestamp(screenshot_file.stat().st_mtime)
            
            print(f"   {i:2d}. {screenshot_file.name}")
            print(f"       📊 Size: {file_size:,} bytes")
            print(f"       🕐 Created: {created_time.strftime('%H:%M:%S')}")
        
        print(f"\n✅ SUCCESS: All screenshots saved to your Documents folder!")
        print(f"📁 Open Finder and navigate to: Documents/cidb_screenshots")
        print(f"🖼️ You can now view all CIDB portal pages visually")
        
        # Instructions for user
        print(f"\n📋 WHAT YOU CAN DO NOW:")
        print(f"   1. Open Finder")
        print(f"   2. Navigate to Documents → cidb_screenshots")
        print(f"   3. Double-click any PNG file to view")
        print(f"   4. Use screenshots for visual analysis")
        print(f"   5. Share screenshots for manual data extraction")
    else:
        print(f"\n❌ No screenshots were captured")
        print(f"💡 Check internet connection and try again")
    
    return {
        'screenshots_captured': screenshots_captured,
        'total_size_bytes': total_size,
        'output_directory': str(screenshots_dir),
        'files': [f.name for f in screenshot_files]
    }

async def main():
    """Main execution function"""
    try:
        result = await capture_cidb_screenshots()
        
        if result['screenshots_captured'] > 0:
            print(f"\n🎉 MISSION ACCOMPLISHED!")
            print(f"📸 {result['screenshots_captured']} screenshots captured")
            print(f"📁 Location: {result['output_directory']}")
            print(f"💾 Size: {result['total_size_bytes']/1024/1024:.2f} MB")
            
            print(f"\n🎯 SCREENSHOT CAPTURE SUCCESS!")
            print(f"✅ All CIDB portal pages captured")
            print(f"✅ High-resolution PNG images saved")
            print(f"✅ Ready for visual analysis")
            print(f"✅ Saved to your Documents folder")
        else:
            print(f"\n⚠️ No screenshots captured")
            print(f"💡 Check internet connection and try again")
        
        return result
        
    except Exception as e:
        print(f"❌ Screenshot capture failed: {e}")
        return None

if __name__ == "__main__":
    asyncio.run(main())
