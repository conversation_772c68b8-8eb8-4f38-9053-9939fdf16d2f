#!/usr/bin/env python3
"""
Quick CIDB Screenshots
Simple screenshot capture that saves to current directory
"""

import asyncio
import os
from datetime import datetime
from pathlib import Path
import base64
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode

async def capture_quick_screenshots():
    """Capture screenshots quickly"""
    print("📸 QUICK CIDB SCREENSHOT CAPTURE")
    print("=" * 40)
    
    # Create screenshots directory in current folder
    current_dir = Path.cwd()
    screenshots_dir = current_dir / "cidb_screenshots"
    screenshots_dir.mkdir(exist_ok=True)
    
    print(f"📁 Screenshots directory: {screenshots_dir}")
    
    # Simple browser config
    browser_config = BrowserConfig(
        headless=True,
        verbose=False,
        java_script_enabled=True,
        viewport_width=1280,
        viewport_height=720
    )
    
    # Main CIDB URL
    cidb_url = "https://portal.cidb.org.za/RegisterOfContractors/"
    
    screenshots_captured = 0
    
    try:
        async with AsyncWebCrawler(config=browser_config) as crawler:
            print(f"\n📸 Capturing CIDB Portal Screenshot...")
            print(f"   🔗 URL: {cidb_url}")
            
            # Simple config for quick capture
            config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                page_timeout=20000,
                wait_for="css:body",
                screenshot=True,
                delay_before_return_html=2000
            )
            
            result = await crawler.arun(url=cidb_url, config=config)
            
            if result.success and result.screenshot:
                # Save screenshot
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"CIDB_Portal_{timestamp}.png"
                screenshot_path = screenshots_dir / filename
                
                screenshot_data = base64.b64decode(result.screenshot)
                with open(screenshot_path, 'wb') as f:
                    f.write(screenshot_data)
                
                file_size = len(screenshot_data)
                
                print(f"   ✅ Screenshot saved: {filename}")
                print(f"   📊 File size: {file_size:,} bytes")
                print(f"   📁 Path: {screenshot_path}")
                
                screenshots_captured += 1
                
                # Try to capture a few more variations
                for i in range(2, 4):
                    try:
                        print(f"\n📸 Capturing variation {i}...")
                        
                        # Wait a bit longer for each variation
                        config.delay_before_return_html = i * 2000
                        
                        result = await crawler.arun(url=cidb_url, config=config)
                        
                        if result.success and result.screenshot:
                            filename = f"CIDB_Portal_Variation_{i}_{timestamp}.png"
                            screenshot_path = screenshots_dir / filename
                            
                            screenshot_data = base64.b64decode(result.screenshot)
                            with open(screenshot_path, 'wb') as f:
                                f.write(screenshot_data)
                            
                            file_size = len(screenshot_data)
                            
                            print(f"   ✅ Variation {i} saved: {filename}")
                            print(f"   📊 File size: {file_size:,} bytes")
                            
                            screenshots_captured += 1
                        else:
                            print(f"   ❌ Failed to capture variation {i}")
                        
                        await asyncio.sleep(1)
                        
                    except Exception as e:
                        print(f"   ❌ Error with variation {i}: {e}")
                        continue
            else:
                print(f"   ❌ Failed to capture main screenshot")
                if not result.success:
                    print(f"      Error: {result.error_message}")
    
    except Exception as e:
        print(f"❌ Screenshot capture failed: {e}")
    
    # List captured files
    screenshot_files = list(screenshots_dir.glob("*.png"))
    
    print(f"\n🎯 CAPTURE COMPLETED")
    print("=" * 30)
    print(f"📸 Screenshots captured: {screenshots_captured}")
    print(f"📁 Saved to: {screenshots_dir}")
    
    if screenshot_files:
        print(f"\n📋 CAPTURED FILES:")
        for i, screenshot_file in enumerate(screenshot_files, 1):
            file_size = screenshot_file.stat().st_size
            print(f"   {i}. {screenshot_file.name} ({file_size:,} bytes)")
        
        print(f"\n✅ SUCCESS: Screenshots saved!")
        print(f"📁 Check folder: {screenshots_dir}")
        print(f"🖼️ Open PNG files to view CIDB portal")
    else:
        print(f"\n❌ No screenshots captured")
    
    return screenshots_captured

def main():
    """Main function"""
    try:
        result = asyncio.run(capture_quick_screenshots())
        
        if result > 0:
            print(f"\n🎉 SUCCESS: {result} screenshots captured!")
            print(f"📁 Check the 'cidb_screenshots' folder")
            print(f"🖼️ Double-click PNG files to view")
        else:
            print(f"\n⚠️ No screenshots captured")
        
        return result
        
    except Exception as e:
        print(f"❌ Failed: {e}")
        return 0

if __name__ == "__main__":
    main()
