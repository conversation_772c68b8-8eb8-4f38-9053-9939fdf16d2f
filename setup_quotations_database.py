#!/usr/bin/env python3
"""
Setup Quotations Database in Supabase
Creates the necessary tables and indexes for storing Public Works quotations
"""

import os
import logging
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_quotations_database():
    """Create the complete quotations database schema"""
    
    # SQL to create all necessary tables and indexes
    sql_commands = [
        # 1. Create quotations table
        """
        CREATE TABLE IF NOT EXISTS quotations (
            id SERIAL PRIMARY KEY,
            quotation_number TEXT UNIQUE NOT NULL,
            description TEXT,
            submission_at TEXT,
            closing_date_time TEXT,
            closing_date TIMESTAMPTZ,
            invitation_links TEXT[],
            status TEXT DEFAULT 'active',
            scraped_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW(),
            run_id TEXT,
            
            -- Additional useful fields
            department TEXT,
            location TEXT,
            estimated_value DECIMAL,
            category TEXT,
            
            -- Metadata
            created_at TIMESTAMPTZ DEFAULT NOW()
        );
        """,
        
        # 2. Create scraping_runs table
        """
        CREATE TABLE IF NOT EXISTS scraping_runs (
            id SERIAL PRIMARY KEY,
            run_id TEXT UNIQUE NOT NULL,
            status TEXT NOT NULL,
            scraper_type TEXT DEFAULT 'public_works_quotations',
            scraper_version TEXT DEFAULT '1.0.0',
            start_time TIMESTAMPTZ DEFAULT NOW(),
            end_time TIMESTAMPTZ,
            total_quotations_found INTEGER DEFAULT 0,
            new_quotations_added INTEGER DEFAULT 0,
            updated_quotations_count INTEGER DEFAULT 0,
            errors_encountered TEXT[],
            user_agent TEXT,
            notes TEXT,
            
            -- Metadata
            created_at TIMESTAMPTZ DEFAULT NOW()
        );
        """,
        
        # 3. Create quotation_documents table for tracking document links
        """
        CREATE TABLE IF NOT EXISTS quotation_documents (
            id SERIAL PRIMARY KEY,
            quotation_id INTEGER REFERENCES quotations(id) ON DELETE CASCADE,
            quotation_number TEXT NOT NULL,
            document_name TEXT,
            document_url TEXT NOT NULL,
            document_type TEXT DEFAULT 'pdf',
            file_size BIGINT,
            downloaded BOOLEAN DEFAULT FALSE,
            download_path TEXT,
            
            -- Metadata
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        """,
        
        # 4. Create indexes for better performance
        """
        -- Quotations table indexes
        CREATE INDEX IF NOT EXISTS idx_quotations_number ON quotations(quotation_number);
        CREATE INDEX IF NOT EXISTS idx_quotations_closing_date ON quotations(closing_date);
        CREATE INDEX IF NOT EXISTS idx_quotations_status ON quotations(status);
        CREATE INDEX IF NOT EXISTS idx_quotations_scraped_at ON quotations(scraped_at);
        CREATE INDEX IF NOT EXISTS idx_quotations_department ON quotations(department);
        CREATE INDEX IF NOT EXISTS idx_quotations_category ON quotations(category);
        
        -- Scraping runs table indexes
        CREATE INDEX IF NOT EXISTS idx_scraping_runs_run_id ON scraping_runs(run_id);
        CREATE INDEX IF NOT EXISTS idx_scraping_runs_status ON scraping_runs(status);
        CREATE INDEX IF NOT EXISTS idx_scraping_runs_start_time ON scraping_runs(start_time);
        CREATE INDEX IF NOT EXISTS idx_scraping_runs_scraper_type ON scraping_runs(scraper_type);
        
        -- Quotation documents table indexes
        CREATE INDEX IF NOT EXISTS idx_quotation_documents_quotation_id ON quotation_documents(quotation_id);
        CREATE INDEX IF NOT EXISTS idx_quotation_documents_quotation_number ON quotation_documents(quotation_number);
        CREATE INDEX IF NOT EXISTS idx_quotation_documents_downloaded ON quotation_documents(downloaded);
        """,
        
        # 5. Create useful views
        """
        -- View for active quotations with closing dates
        CREATE OR REPLACE VIEW active_quotations AS
        SELECT 
            id,
            quotation_number,
            description,
            submission_at,
            closing_date_time,
            closing_date,
            CASE 
                WHEN closing_date > NOW() THEN 'open'
                WHEN closing_date <= NOW() THEN 'closed'
                ELSE 'unknown'
            END as tender_status,
            EXTRACT(DAYS FROM (closing_date - NOW())) as days_until_closing,
            array_length(invitation_links, 1) as document_count,
            department,
            location,
            category,
            scraped_at
        FROM quotations 
        WHERE status = 'active'
        ORDER BY closing_date ASC;
        """,
        
        # 6. Create function to update updated_at timestamp
        """
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ language 'plpgsql';
        """,
        
        # 7. Create triggers for updated_at
        """
        DROP TRIGGER IF EXISTS update_quotations_updated_at ON quotations;
        CREATE TRIGGER update_quotations_updated_at 
            BEFORE UPDATE ON quotations 
            FOR EACH ROW 
            EXECUTE FUNCTION update_updated_at_column();
            
        DROP TRIGGER IF EXISTS update_quotation_documents_updated_at ON quotation_documents;
        CREATE TRIGGER update_quotation_documents_updated_at 
            BEFORE UPDATE ON quotation_documents 
            FOR EACH ROW 
            EXECUTE FUNCTION update_updated_at_column();
        """,
        
        # 8. Create RLS (Row Level Security) policies if needed
        """
        -- Enable RLS on tables (optional, for security)
        -- ALTER TABLE quotations ENABLE ROW LEVEL SECURITY;
        -- ALTER TABLE scraping_runs ENABLE ROW LEVEL SECURITY;
        -- ALTER TABLE quotation_documents ENABLE ROW LEVEL SECURITY;
        
        -- Create policies (example - adjust based on your needs)
        -- CREATE POLICY "Public quotations are viewable by everyone" ON quotations
        --     FOR SELECT USING (true);
        """
    ]
    
    logger.info("🗃️ Creating quotations database schema...")
    
    for i, sql_command in enumerate(sql_commands, 1):
        try:
            logger.info(f"Executing SQL command {i}/{len(sql_commands)}...")
            
            # Note: Supabase Python client doesn't directly support raw SQL execution
            # You'll need to run these commands in the Supabase SQL editor
            print(f"\n--- SQL Command {i} ---")
            print(sql_command.strip())
            print("-" * 50)
            
        except Exception as e:
            logger.error(f"Error executing SQL command {i}: {e}")
    
    logger.info("✅ Database schema SQL generated!")
    logger.info("📝 Please copy and paste the SQL commands above into your Supabase SQL editor")
    logger.info("🔗 Go to: https://app.supabase.com/project/YOUR_PROJECT/sql")

def verify_database_setup():
    """Verify that the database tables were created successfully"""
    try:
        logger.info("🔍 Verifying database setup...")
        
        # Test quotations table
        try:
            result = supabase.table('quotations').select('*').limit(1).execute()
            logger.info("✅ Quotations table exists and is accessible")
        except Exception as e:
            logger.error(f"❌ Quotations table issue: {e}")
        
        # Test scraping_runs table
        try:
            result = supabase.table('scraping_runs').select('*').limit(1).execute()
            logger.info("✅ Scraping runs table exists and is accessible")
        except Exception as e:
            logger.error(f"❌ Scraping runs table issue: {e}")
        
        # Test quotation_documents table
        try:
            result = supabase.table('quotation_documents').select('*').limit(1).execute()
            logger.info("✅ Quotation documents table exists and is accessible")
        except Exception as e:
            logger.error(f"❌ Quotation documents table issue: {e}")
        
        logger.info("🎯 Database verification completed!")
        
    except Exception as e:
        logger.error(f"Error during verification: {e}")

def main():
    """Main execution function"""
    print("🏛️ PUBLIC WORKS QUOTATIONS DATABASE SETUP")
    print("=" * 50)
    
    if not SUPABASE_URL or not SUPABASE_KEY:
        print("❌ Error: SUPABASE_URL and SUPABASE_KEY must be set in .env file")
        return
    
    print(f"🔗 Supabase URL: {SUPABASE_URL}")
    print()
    
    # Generate database schema
    create_quotations_database()
    
    print("\n" + "=" * 50)
    print("📋 NEXT STEPS:")
    print("1. Copy the SQL commands above")
    print("2. Go to your Supabase project dashboard")
    print("3. Navigate to SQL Editor")
    print("4. Paste and execute the SQL commands")
    print("5. Run the verification script")
    print()
    print("After creating the tables, run:")
    print("python3 setup_quotations_database.py --verify")

if __name__ == "__main__":
    import sys
    
    if "--verify" in sys.argv:
        verify_database_setup()
    else:
        main()
