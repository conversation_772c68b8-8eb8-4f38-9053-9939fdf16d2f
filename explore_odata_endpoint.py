#!/usr/bin/env python3
"""
Explore the OData endpoint found on CIDB portal
"""

import requests
import xml.etree.ElementTree as ET
import logging
from bs4 import BeautifulSoup

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def explore_odata_service():
    """Explore the OData service metadata"""
    
    base_url = "https://portal.cidb.org.za"
    odata_url = f"{base_url}/_odata/"
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36",
        "Accept": "application/xml,text/xml,*/*",
        "Accept-Language": "en-US,en;q=0.5",
    }
    
    session = requests.Session()
    session.headers.update(headers)
    
    try:
        logger.info(f"Exploring OData service: {odata_url}")
        response = session.get(odata_url, timeout=30)
        response.raise_for_status()
        
        logger.info(f"Response status: {response.status_code}")
        logger.info(f"Content type: {response.headers.get('content-type')}")
        
        # Save the full response
        with open('odata_service_document.xml', 'w', encoding='utf-8') as f:
            f.write(response.text)
        logger.info("Saved OData service document to 'odata_service_document.xml'")
        
        # Parse the XML to find available collections
        try:
            root = ET.fromstring(response.text)
            
            # Find all collection elements
            collections = []
            for elem in root.iter():
                if 'collection' in elem.tag.lower():
                    href = elem.get('href', '')
                    if href:
                        collections.append(href)
                        logger.info(f"Found collection: {href}")
            
            # Try to get metadata
            metadata_url = f"{odata_url}$metadata"
            logger.info(f"Trying metadata endpoint: {metadata_url}")
            
            metadata_response = session.get(metadata_url, timeout=30)
            if metadata_response.status_code == 200:
                logger.info("Successfully retrieved metadata!")
                
                with open('odata_metadata.xml', 'w', encoding='utf-8') as f:
                    f.write(metadata_response.text)
                logger.info("Saved metadata to 'odata_metadata.xml'")
                
                # Parse metadata to find entity types
                try:
                    metadata_root = ET.fromstring(metadata_response.text)
                    
                    # Look for EntityType elements
                    entity_types = []
                    for elem in metadata_root.iter():
                        if 'EntityType' in elem.tag:
                            name = elem.get('Name', '')
                            if name:
                                entity_types.append(name)
                                logger.info(f"Found EntityType: {name}")
                    
                    # Look for EntitySet elements  
                    entity_sets = []
                    for elem in metadata_root.iter():
                        if 'EntitySet' in elem.tag:
                            name = elem.get('Name', '')
                            entity_type = elem.get('EntityType', '')
                            if name:
                                entity_sets.append((name, entity_type))
                                logger.info(f"Found EntitySet: {name} (Type: {entity_type})")
                    
                    return collections, entity_types, entity_sets
                    
                except ET.ParseError as e:
                    logger.error(f"Error parsing metadata XML: {e}")
                    
            else:
                logger.warning(f"Metadata endpoint returned status: {metadata_response.status_code}")
            
            return collections, [], []
            
        except ET.ParseError as e:
            logger.error(f"Error parsing service document XML: {e}")
            return [], [], []
            
    except Exception as e:
        logger.error(f"Error exploring OData service: {e}")
        return [], [], []

def test_entity_collections(collections, entity_sets):
    """Test the discovered entity collections"""
    
    base_url = "https://portal.cidb.org.za"
    odata_url = f"{base_url}/_odata/"
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36",
        "Accept": "application/json,application/xml,*/*",
        "Accept-Language": "en-US,en;q=0.5",
    }
    
    session = requests.Session()
    session.headers.update(headers)
    
    # Test collections from service document
    for collection in collections:
        url = f"{odata_url}{collection}"
        logger.info(f"Testing collection: {url}")
        
        try:
            response = session.get(url, timeout=15)
            logger.info(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                logger.info(f"SUCCESS! Collection {collection} is accessible")
                logger.info(f"Content preview: {response.text[:200]}...")
                
                # Try to save a sample
                with open(f'collection_{collection.replace("/", "_")}.xml', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                    
        except Exception as e:
            logger.error(f"Error testing collection {collection}: {e}")
    
    # Test entity sets
    for entity_name, entity_type in entity_sets:
        url = f"{odata_url}{entity_name}"
        logger.info(f"Testing entity set: {url}")
        
        try:
            response = session.get(url, timeout=15)
            logger.info(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                logger.info(f"SUCCESS! Entity set {entity_name} is accessible")
                logger.info(f"Content preview: {response.text[:200]}...")
                
                # Try to save a sample
                with open(f'entityset_{entity_name}.xml', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                    
        except Exception as e:
            logger.error(f"Error testing entity set {entity_name}: {e}")

def main():
    logger.info("Starting OData exploration...")
    
    collections, entity_types, entity_sets = explore_odata_service()
    
    logger.info(f"Found {len(collections)} collections")
    logger.info(f"Found {len(entity_types)} entity types") 
    logger.info(f"Found {len(entity_sets)} entity sets")
    
    if collections or entity_sets:
        logger.info("Testing discovered collections and entity sets...")
        test_entity_collections(collections, entity_sets)
    else:
        logger.warning("No collections or entity sets found to test")

if __name__ == "__main__":
    main()
