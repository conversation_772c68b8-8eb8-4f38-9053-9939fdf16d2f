#!/usr/bin/env python3
"""
Fix for FireCrawl API integration - Updated to match new API format
"""

import os
from firecrawl import FirecrawlApp
from dotenv import load_dotenv

load_dotenv()

def test_firecrawl_new_format():
    """Test FireCrawl with corrected API format"""
    
    api_key = os.getenv('FIRECRAWL_API_KEY')
    if not api_key:
        print("❌ FIRECRAWL_API_KEY not found in .env")
        return
    
    app = FirecrawlApp(api_key=api_key)
    
    print("🔥 Testing FireCrawl API with correct format...")
    
    # Correct format - no 'params' wrapper
    try:
        # Method 1: Simple scrape
        print("\n1️⃣ Simple scrape test:")
        result = app.scrape_url(
            url='https://httpbin.org/html',
            formats=['markdown', 'html']  # Correct parameter name
        )
        print("✅ Simple scrape successful!")
        print(f"   Content length: {len(result.get('markdown', ''))} chars")
        
    except Exception as e:
        print(f"❌ Simple scrape failed: {str(e)}")
    
    try:
        # Method 2: Scrape with extraction
        print("\n2️⃣ Scrape with schema extraction:")
        
        # Define extraction schema
        schema = {
            "type": "object",
            "properties": {
                "contractors": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "crs_number": {"type": "string"},
                            "contractor_name": {"type": "string"},
                            "status": {"type": "string"},
                            "province": {"type": "string"}
                        }
                    }
                }
            }
        }
        
        result = app.scrape_url(
            url='https://httpbin.org/html',
            formats=['markdown', 'extract'],
            extract={
                "schema": schema,
                "systemPrompt": "Extract contractor information from the page"
            }
        )
        print("✅ Schema extraction successful!")
        
    except Exception as e:
        print(f"❌ Schema extraction failed: {str(e)}")
        print("   This might be a paid feature")

def create_fixed_firecrawl_method():
    """Create a fixed version of the firecrawl_scrape method"""
    
    code = '''
async def firecrawl_scrape_fixed(self, url: str, extract_schema: Optional[Dict] = None) -> Dict:
    """Fixed FireCrawl scraping method with correct API format"""
    if not firecrawl_app:
        logger.warning("FireCrawl API key not configured")
        return {}
    
    try:
        # Apply rate limiting
        rate_limited = await self.rate_limiter.wait_if_needed()
        if rate_limited:
            self.stats.rate_limit_hits += 1
        
        logger.info(f"🔥 FireCrawl scraping: {url}")
        
        # Correct API format - no 'params' wrapper
        if extract_schema:
            # With extraction
            result = firecrawl_app.scrape_url(
                url=url,
                formats=['markdown', 'extract', 'screenshot'],
                extract={
                    "schema": extract_schema,
                    "systemPrompt": "Extract CIDB contractor information"
                }
            )
        else:
            # Simple scrape
            result = firecrawl_app.scrape_url(
                url=url,
                formats=['markdown', 'html', 'screenshot']
            )
        
        self.stats.firecrawl_requests += 1
        
        # Process results
        output = {
            'url': url,
            'markdown': result.get('markdown', ''),
            'html': result.get('html', ''),
            'extracted_data': result.get('extract', {}) if extract_schema else None,
            'screenshot': result.get('screenshot', None),
            'method': 'firecrawl'
        }
        
        logger.info(f"✅ FireCrawl scraped {len(output['markdown'])} chars")
        return output
        
    except Exception as e:
        logger.error(f"FireCrawl scraping failed: {str(e)}")
        self.stats.errors.append(f"FireCrawl error: {str(e)}")
        return {}
'''
    
    print("\n📝 Fixed FireCrawl method created!")
    print("To apply this fix, replace the firecrawl_scrape method in super_hybrid_scraper.py")
    
    with open('firecrawl_fix.txt', 'w') as f:
        f.write(code)
    print("✅ Fix saved to firecrawl_fix.txt")

if __name__ == "__main__":
    print("🔧 FireCrawl API Fix Tool")
    print("=" * 50)
    
    # Test new format
    test_firecrawl_new_format()
    
    # Generate fix
    create_fixed_firecrawl_method()
    
    print("\n✅ Fix complete! The issue was:")
    print("   - Old format used 'params' parameter (deprecated)")
    print("   - New format uses direct parameters")
    print("\n💡 To fix your scraper:")
    print("   1. Apply the fix from firecrawl_fix.txt")
    print("   2. Or use Crawl4AI method (already working)")
    print("   3. Or use the working example script")