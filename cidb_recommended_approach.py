#!/usr/bin/env python3
"""
CIDB Recommended Scraping Approach
Since direct API calls don't work with Power Platform portals,
this script demonstrates the recommended browser automation approach.
"""

import asyncio
import logging
from datetime import datetime
import pandas as pd
import json

# This would use your existing Crawl4AI setup
from crawl4ai import Async<PERSON>ebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CIDBRecommendedScraper:
    """
    Recommended approach for scraping CIDB Power Platform portal
    Uses browser automation to interact with the dynamic data grid
    """
    
    def __init__(self):
        self.base_url = "https://portal.cidb.org.za/RegisterOfContractors/"
        self.browser_config = BrowserConfig(
            headless=False,  # Set to True for production
            verbose=True,
            java_script_enabled=True,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        )
    
    async def wait_for_data_grid_to_load(self, crawler):
        """Wait for the Power Platform data grid to fully load"""
        
        wait_script = """
        (async () => {
            // Wait for Power Platform components to initialize
            let attempts = 0;
            const maxAttempts = 30;
            
            while (attempts < maxAttempts) {
                // Look for the data grid container
                const gridContainer = document.querySelector('.view-grid');
                const dataRows = document.querySelectorAll('[data-testid="DetailsList"] .ms-DetailsRow, .data-row, tr[data-row]');
                
                if (gridContainer && dataRows.length > 0) {
                    console.log(`Found ${dataRows.length} data rows`);
                    return true;
                }
                
                // Also check for loading indicators
                const loadingIndicators = document.querySelectorAll('.fa-spinner, .loading, [aria-label*="loading"]');
                if (loadingIndicators.length === 0 && attempts > 10) {
                    // No loading indicators and we've waited a bit
                    break;
                }
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                attempts++;
            }
            
            return false;
        })();
        """
        
        config = CrawlerRunConfig(
            js_code=[wait_script],
            wait_for="networkidle",
            cache_mode=CacheMode.BYPASS
        )
        
        result = await crawler.arun(url=self.base_url, config=config)
        return result.success and "true" in str(result.js_execution_result)
    
    async def extract_contractor_data(self, crawler):
        """Extract contractor data from the loaded grid"""
        
        # Multiple extraction strategies for different possible grid structures
        extraction_strategies = [
            # Strategy 1: Power Platform Fluent UI DetailsList
            {
                "name": "Power Platform DetailsList",
                "baseSelector": "[data-testid='DetailsList'] .ms-DetailsRow",
                "fields": [
                    {"name": "crs_number", "selector": ".ms-DetailsRow-cell:nth-child(1)", "type": "text"},
                    {"name": "contractor_name", "selector": ".ms-DetailsRow-cell:nth-child(2)", "type": "text"},
                    {"name": "status", "selector": ".ms-DetailsRow-cell:nth-child(3)", "type": "text"},
                    {"name": "grading", "selector": ".ms-DetailsRow-cell:nth-child(4)", "type": "text"},
                    {"name": "expiry_date", "selector": ".ms-DetailsRow-cell:nth-child(5)", "type": "text"},
                    {"name": "trading_name", "selector": ".ms-DetailsRow-cell:nth-child(6)", "type": "text"},
                    {"name": "province", "selector": ".ms-DetailsRow-cell:nth-child(7)", "type": "text"},
                    {"name": "city", "selector": ".ms-DetailsRow-cell:nth-child(8)", "type": "text"},
                ]
            },
            # Strategy 2: Generic data grid
            {
                "name": "Generic Data Grid",
                "baseSelector": ".data-row, tr[data-row]",
                "fields": [
                    {"name": "crs_number", "selector": "td:nth-child(1), .cell:nth-child(1)", "type": "text"},
                    {"name": "contractor_name", "selector": "td:nth-child(2), .cell:nth-child(2)", "type": "text"},
                    {"name": "status", "selector": "td:nth-child(3), .cell:nth-child(3)", "type": "text"},
                    {"name": "grading", "selector": "td:nth-child(4), .cell:nth-child(4)", "type": "text"},
                    {"name": "expiry_date", "selector": "td:nth-child(5), .cell:nth-child(5)", "type": "text"},
                    {"name": "trading_name", "selector": "td:nth-child(6), .cell:nth-child(6)", "type": "text"},
                    {"name": "province", "selector": "td:nth-child(7), .cell:nth-child(7)", "type": "text"},
                    {"name": "city", "selector": "td:nth-child(8), .cell:nth-child(8)", "type": "text"},
                ]
            },
            # Strategy 3: Table-based structure
            {
                "name": "Table Structure",
                "baseSelector": "table tbody tr:not(:first-child)",
                "fields": [
                    {"name": "crs_number", "selector": "td:nth-child(1)", "type": "text"},
                    {"name": "contractor_name", "selector": "td:nth-child(2)", "type": "text"},
                    {"name": "status", "selector": "td:nth-child(3)", "type": "text"},
                    {"name": "grading", "selector": "td:nth-child(4)", "type": "text"},
                    {"name": "expiry_date", "selector": "td:nth-child(5)", "type": "text"},
                    {"name": "trading_name", "selector": "td:nth-child(6)", "type": "text"},
                    {"name": "province", "selector": "td:nth-child(7)", "type": "text"},
                    {"name": "city", "selector": "td:nth-child(8)", "type": "text"},
                ]
            }
        ]
        
        for strategy in extraction_strategies:
            try:
                logger.info(f"Trying extraction strategy: {strategy['name']}")
                
                extraction_strategy = JsonCssExtractionStrategy(strategy, verbose=True)
                
                config = CrawlerRunConfig(
                    extraction_strategy=extraction_strategy,
                    cache_mode=CacheMode.BYPASS
                )
                
                result = await crawler.arun(url=self.base_url, config=config)
                
                if result.success and result.extracted_content:
                    data = json.loads(result.extracted_content)
                    if data and len(data) > 0:
                        logger.info(f"Successfully extracted {len(data)} records using {strategy['name']}")
                        return data
                
            except Exception as e:
                logger.warning(f"Strategy {strategy['name']} failed: {e}")
                continue
        
        return []
    
    async def handle_pagination(self, crawler):
        """Handle pagination to get all contractor data"""
        
        all_contractors = []
        page_num = 1
        
        while True:
            logger.info(f"Processing page {page_num}")
            
            # Wait for data to load
            if not await self.wait_for_data_grid_to_load(crawler):
                logger.warning(f"Data grid didn't load properly on page {page_num}")
                break
            
            # Extract data from current page
            page_data = await self.extract_contractor_data(crawler)
            
            if not page_data:
                logger.warning(f"No data extracted from page {page_num}")
                break
            
            all_contractors.extend(page_data)
            logger.info(f"Extracted {len(page_data)} contractors from page {page_num}")
            
            # Try to go to next page
            next_page_script = """
            (async () => {
                // Look for various pagination button patterns
                const nextButtons = [
                    document.querySelector('[aria-label*="next" i]'),
                    document.querySelector('[title*="next" i]'),
                    document.querySelector('.pagination .next'),
                    document.querySelector('[data-testid*="next" i]'),
                    document.querySelector('button:contains("Next")'),
                    document.querySelector('a:contains("Next")')
                ];
                
                for (const button of nextButtons) {
                    if (button && !button.disabled && !button.classList.contains('disabled')) {
                        button.click();
                        await new Promise(resolve => setTimeout(resolve, 3000));
                        return true;
                    }
                }
                
                return false;
            })();
            """
            
            config = CrawlerRunConfig(
                js_code=[next_page_script],
                cache_mode=CacheMode.BYPASS
            )
            
            result = await crawler.arun(url=self.base_url, config=config)
            
            if not (result.success and "true" in str(result.js_execution_result)):
                logger.info("No more pages available")
                break
            
            page_num += 1
            
            # Add delay between pages
            await asyncio.sleep(2)
        
        return all_contractors
    
    async def scrape_all_contractors(self):
        """Main scraping method"""
        logger.info("Starting CIDB contractor scraping with browser automation...")
        
        async with AsyncWebCrawler(config=self.browser_config) as crawler:
            # Get all contractor data
            all_contractors = await self.handle_pagination(crawler)
            
            if all_contractors:
                # Save to CSV
                df = pd.DataFrame(all_contractors)
                
                # Clean and process data
                for col in df.columns:
                    if df[col].dtype == 'object':
                        df[col] = df[col].astype(str).str.strip()
                
                # Add metadata
                df['scraped_at'] = datetime.utcnow().isoformat()
                
                # Save to CSV
                filename = f"cidb_contractors_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                df.to_csv(filename, index=False, encoding='utf-8-sig')
                
                logger.info(f"Successfully scraped {len(all_contractors)} contractors")
                logger.info(f"Data saved to {filename}")
                
                return all_contractors
            else:
                logger.error("No contractor data was extracted")
                return []

async def main():
    """Main execution function"""
    scraper = CIDBRecommendedScraper()
    
    try:
        contractors = await scraper.scrape_all_contractors()
        
        if contractors:
            print(f"\n✅ SUCCESS!")
            print(f"Scraped {len(contractors)} contractors")
            print(f"Sample data: {contractors[0] if contractors else 'None'}")
        else:
            print("\n❌ No data was scraped")
            print("The Power Platform data grid structure may have changed")
            print("Manual inspection and selector adjustment may be required")
            
    except Exception as e:
        logger.error(f"Scraping failed: {e}")
        print(f"\n❌ Scraping failed: {e}")

if __name__ == "__main__":
    print("CIDB Recommended Scraping Approach")
    print("=" * 50)
    print("This approach uses browser automation to interact with")
    print("the Power Platform data grid component.")
    print()
    
    # Uncomment the line below to run the scraper
    # asyncio.run(main())
    
    print("To run this scraper:")
    print("1. Uncomment the asyncio.run(main()) line above")
    print("2. Ensure you have crawl4ai installed")
    print("3. Run: python3 cidb_recommended_approach.py")
    print()
    print("This approach will:")
    print("- Load the page with a real browser")
    print("- Wait for the data grid to load")
    print("- Extract contractor data using multiple strategies")
    print("- Handle pagination automatically")
    print("- Save results to CSV with timestamps")
