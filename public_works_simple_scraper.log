2025-07-10 20:42:55,837 - INFO - 🚀 Starting Public Works awarded tenders scraping...
2025-07-10 20:42:56,859 - INFO - Scraping current awarded tenders...
2025-07-10 20:43:02,431 - INFO - Scraped 156 current awarded tenders
2025-07-10 20:43:02,432 - INFO - Saving 156 awarded tenders to Supabase...
2025-07-10 20:43:02,437 - ERROR - Error saving awarded tenders batch: Object of type date is not JSON serializable
2025-07-10 20:43:02,437 - ERROR - Error saving awarded tenders batch: Object of type date is not JSON serializable
2025-07-10 20:43:02,437 - ERROR - Error saving awarded tenders batch: Object of type date is not JSON serializable
2025-07-10 20:43:02,438 - ERROR - Error saving awarded tenders batch: Object of type date is not JSON serializable
2025-07-10 20:43:02,438 - INFO - ✅ Saved 0/156 awarded tenders
2025-07-10 20:43:02,521 - INFO - 
🎯 Scraping completed in 6.68 seconds
Run ID: 665c7178-b921-48a0-abc8-d3917ddcfa01
Current Awarded Tenders: 156
            
2025-07-10 20:43:27,855 - INFO - 🚀 Starting Public Works awarded tenders scraping...
2025-07-10 20:43:28,692 - INFO - Scraping current awarded tenders...
2025-07-10 20:43:34,113 - INFO - Scraped 156 current awarded tenders
2025-07-10 20:43:34,113 - INFO - Saving 156 awarded tenders to Supabase...
2025-07-10 20:43:35,104 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/awarded_tenders?on_conflict=bid_number&columns=%22contract_value_text%22%2C%22bbee_level%22%2C%22bid_number%22%2C%22scraped_at%22%2C%22run_id%22%2C%22service_description%22%2C%22contract_value%22%2C%22archive_year%22%2C%22date_awarded%22%2C%22data_source%22%2C%22contractor_name%22 "HTTP/2 201 Created"
2025-07-10 20:43:35,112 - INFO - ✅ Saved awarded tenders batch 1
2025-07-10 20:43:35,861 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/awarded_tenders?on_conflict=bid_number&columns=%22contract_value_text%22%2C%22bbee_level%22%2C%22bid_number%22%2C%22scraped_at%22%2C%22run_id%22%2C%22service_description%22%2C%22contract_value%22%2C%22archive_year%22%2C%22date_awarded%22%2C%22data_source%22%2C%22contractor_name%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:43:35,863 - ERROR - Error saving awarded tenders batch: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:43:36,151 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/awarded_tenders?on_conflict=bid_number&columns=%22contract_value_text%22%2C%22bbee_level%22%2C%22bid_number%22%2C%22scraped_at%22%2C%22run_id%22%2C%22service_description%22%2C%22contract_value%22%2C%22archive_year%22%2C%22date_awarded%22%2C%22data_source%22%2C%22contractor_name%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:43:36,151 - ERROR - Error saving awarded tenders batch: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:43:36,372 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/awarded_tenders?on_conflict=bid_number&columns=%22contract_value_text%22%2C%22bbee_level%22%2C%22bid_number%22%2C%22scraped_at%22%2C%22run_id%22%2C%22service_description%22%2C%22contract_value%22%2C%22archive_year%22%2C%22date_awarded%22%2C%22data_source%22%2C%22contractor_name%22 "HTTP/2 201 Created"
2025-07-10 20:43:36,374 - INFO - ✅ Saved awarded tenders batch 4
2025-07-10 20:43:36,374 - INFO - ✅ Saved 56/156 awarded tenders
2025-07-10 20:43:36,475 - INFO - 
🎯 Scraping completed in 8.62 seconds
Run ID: a4b35ae6-d277-4c43-922e-39d6b6b6ff73
Current Awarded Tenders: 156
            
