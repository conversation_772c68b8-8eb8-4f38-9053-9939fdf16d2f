#!/usr/bin/env python3
"""
Public Works Quotations Dashboard
View and analyze scraped quotations data from Supabase
"""

import os
import pandas as pd
from datetime import datetime, timedelta
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

class QuotationsDashboard:
    """Dashboard for analyzing Public Works quotations"""
    
    def __init__(self):
        self.df = None
        self.load_data()
    
    def load_data(self):
        """Load quotations data from Supabase"""
        try:
            print("📊 Loading quotations data from Supabase...")
            
            # Get all quotations
            result = supabase.table('quotations').select('*').execute()
            
            if result.data:
                self.df = pd.DataFrame(result.data)
                
                # Convert date columns
                if 'closing_date' in self.df.columns:
                    self.df['closing_date'] = pd.to_datetime(self.df['closing_date'])
                if 'scraped_at' in self.df.columns:
                    self.df['scraped_at'] = pd.to_datetime(self.df['scraped_at'])
                
                print(f"✅ Loaded {len(self.df)} quotations")
            else:
                print("❌ No quotations data found")
                self.df = pd.DataFrame()
                
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            self.df = pd.DataFrame()
    
    def show_summary_stats(self):
        """Display summary statistics"""
        if self.df.empty:
            print("No data available")
            return
        
        print("\n" + "="*60)
        print("📈 QUOTATIONS SUMMARY STATISTICS")
        print("="*60)
        
        print(f"Total Quotations: {len(self.df):,}")
        
        # Status breakdown
        if 'status' in self.df.columns:
            status_counts = self.df['status'].value_counts()
            print(f"\nStatus Breakdown:")
            for status, count in status_counts.items():
                print(f"  • {status}: {count:,}")
        
        # Date range
        if 'closing_date' in self.df.columns and not self.df['closing_date'].isna().all():
            min_date = self.df['closing_date'].min()
            max_date = self.df['closing_date'].max()
            print(f"\nClosing Date Range:")
            print(f"  • Earliest: {min_date.strftime('%Y-%m-%d') if pd.notna(min_date) else 'N/A'}")
            print(f"  • Latest: {max_date.strftime('%Y-%m-%d') if pd.notna(max_date) else 'N/A'}")
        
        # Recent scraping info
        if 'scraped_at' in self.df.columns:
            latest_scrape = self.df['scraped_at'].max()
            print(f"\nLast Scraped: {latest_scrape.strftime('%Y-%m-%d %H:%M:%S') if pd.notna(latest_scrape) else 'N/A'}")
    
    def show_recent_quotations(self, limit=10):
        """Show most recent quotations"""
        if self.df.empty:
            print("No data available")
            return
        
        print(f"\n" + "="*60)
        print(f"🕒 MOST RECENT {limit} QUOTATIONS")
        print("="*60)
        
        # Sort by scraped_at or closing_date
        sort_column = 'scraped_at' if 'scraped_at' in self.df.columns else 'closing_date'
        recent_df = self.df.sort_values(sort_column, ascending=False).head(limit)
        
        for idx, row in recent_df.iterrows():
            print(f"\n📋 {row.get('quotation_number', 'N/A')}")
            
            # Description (truncated)
            desc = row.get('description', 'N/A')
            if len(desc) > 100:
                desc = desc[:100] + "..."
            print(f"   Description: {desc}")
            
            # Closing date
            closing_date = row.get('closing_date_time', 'N/A')
            print(f"   Closing: {closing_date}")
            
            # Submission location (truncated)
            submission = row.get('submission_at', 'N/A')
            if len(submission) > 80:
                submission = submission[:80] + "..."
            print(f"   Submit at: {submission}")
    
    def show_closing_soon(self, days=30):
        """Show quotations closing soon"""
        if self.df.empty or 'closing_date' not in self.df.columns:
            print("No closing date data available")
            return
        
        print(f"\n" + "="*60)
        print(f"⏰ QUOTATIONS CLOSING IN NEXT {days} DAYS")
        print("="*60)
        
        # Filter for quotations closing soon
        now = datetime.now()
        future_date = now + timedelta(days=days)
        
        closing_soon = self.df[
            (self.df['closing_date'] >= now) & 
            (self.df['closing_date'] <= future_date)
        ].sort_values('closing_date')
        
        if closing_soon.empty:
            print(f"No quotations closing in the next {days} days")
            return
        
        print(f"Found {len(closing_soon)} quotations closing soon:")
        
        for idx, row in closing_soon.iterrows():
            closing_date = row['closing_date']
            days_left = (closing_date - now).days
            
            print(f"\n🔔 {row.get('quotation_number', 'N/A')} - {days_left} days left")
            print(f"   Closes: {closing_date.strftime('%Y-%m-%d %H:%M')}")
            
            desc = row.get('description', 'N/A')
            if len(desc) > 80:
                desc = desc[:80] + "..."
            print(f"   Description: {desc}")
    
    def search_quotations(self, search_term):
        """Search quotations by keyword"""
        if self.df.empty:
            print("No data available")
            return
        
        print(f"\n" + "="*60)
        print(f"🔍 SEARCH RESULTS FOR: '{search_term}'")
        print("="*60)
        
        # Search in quotation_number and description
        mask = (
            self.df['quotation_number'].str.contains(search_term, case=False, na=False) |
            self.df['description'].str.contains(search_term, case=False, na=False)
        )
        
        results = self.df[mask]
        
        if results.empty:
            print(f"No quotations found containing '{search_term}'")
            return
        
        print(f"Found {len(results)} matching quotations:")
        
        for idx, row in results.head(10).iterrows():  # Show top 10 results
            print(f"\n📋 {row.get('quotation_number', 'N/A')}")
            
            desc = row.get('description', 'N/A')
            if len(desc) > 100:
                desc = desc[:100] + "..."
            print(f"   Description: {desc}")
            
            print(f"   Closing: {row.get('closing_date_time', 'N/A')}")
        
        if len(results) > 10:
            print(f"\n... and {len(results) - 10} more results")
    
    def export_to_csv(self, filename=None):
        """Export quotations data to CSV"""
        if self.df.empty:
            print("No data to export")
            return
        
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"quotations_export_{timestamp}.csv"
        
        try:
            self.df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"✅ Exported {len(self.df)} quotations to {filename}")
        except Exception as e:
            print(f"❌ Error exporting data: {e}")
    
    def show_menu(self):
        """Display interactive menu"""
        while True:
            print("\n" + "="*60)
            print("🏛️ PUBLIC WORKS QUOTATIONS DASHBOARD")
            print("="*60)
            print("1. Summary Statistics")
            print("2. Recent Quotations")
            print("3. Closing Soon")
            print("4. Search Quotations")
            print("5. Export to CSV")
            print("6. Refresh Data")
            print("0. Exit")
            print("-" * 60)
            
            choice = input("Select an option (0-6): ").strip()
            
            if choice == '0':
                print("👋 Goodbye!")
                break
            elif choice == '1':
                self.show_summary_stats()
            elif choice == '2':
                limit = input("Number of recent quotations to show (default 10): ").strip()
                limit = int(limit) if limit.isdigit() else 10
                self.show_recent_quotations(limit)
            elif choice == '3':
                days = input("Show quotations closing in how many days? (default 30): ").strip()
                days = int(days) if days.isdigit() else 30
                self.show_closing_soon(days)
            elif choice == '4':
                search_term = input("Enter search term: ").strip()
                if search_term:
                    self.search_quotations(search_term)
                else:
                    print("Please enter a search term")
            elif choice == '5':
                filename = input("Enter filename (press Enter for auto-generated): ").strip()
                filename = filename if filename else None
                self.export_to_csv(filename)
            elif choice == '6':
                self.load_data()
            else:
                print("Invalid option. Please try again.")

def main():
    """Main execution function"""
    print("🏛️ PUBLIC WORKS QUOTATIONS DASHBOARD")
    print("=" * 60)
    
    if not SUPABASE_URL or not SUPABASE_KEY:
        print("❌ Error: SUPABASE_URL and SUPABASE_KEY must be set in .env file")
        return
    
    dashboard = QuotationsDashboard()
    
    if dashboard.df.empty:
        print("❌ No quotations data available. Please run the scraper first.")
        return
    
    # Show quick summary
    dashboard.show_summary_stats()
    
    # Start interactive menu
    dashboard.show_menu()

if __name__ == "__main__":
    main()
