#!/usr/bin/env python3
"""
Public Works Awarded Tenders Simple Scraper
Focuses on scraping and immediately saving awarded tenders data
"""

import asyncio
import json
import os
import uuid
from datetime import datetime
from typing import List, Dict, Optional
import logging
import re

from crawl4ai import Async<PERSON>ebCrawler, <PERSON>rowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')
PUBLIC_WORKS_BASE_URL = "http://www.publicworks.gov.za"

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('public_works_simple_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PublicWorksSimpleScraper:
    """Simple Public Works Awarded Tenders Scraper"""
    
    def __init__(self):
        self.run_id = str(uuid.uuid4())
        self.browser_config = BrowserConfig(
            headless=True,
            verbose=False,
            java_script_enabled=True,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        )
    
    def parse_contract_value(self, value_text: str) -> tuple:
        """Parse contract value from text"""
        try:
            if not value_text:
                return None, None
            
            clean_text = value_text.strip()
            value_match = re.search(r'R\s*([\d\s,\.]+)', clean_text)
            if value_match:
                value_str = value_match.group(1).replace(' ', '').replace(',', '')
                try:
                    numeric_value = float(value_str)
                    return numeric_value, clean_text
                except ValueError:
                    return None, clean_text
            
            return None, clean_text
            
        except Exception as e:
            logger.warning(f"Error parsing contract value '{value_text}': {e}")
            return None, value_text
    
    def extract_bbee_level(self, contractor_text: str) -> tuple:
        """Extract B-BBEE level from contractor text"""
        try:
            if not contractor_text:
                return contractor_text, None

            # Look for various B-BBEE patterns
            bbee_patterns = [
                r'\*\*B-BBEE level (\d+)\*\*',
                r'B-BBEE level\s*(\d+)',
                r'B-BBEE\s*level\s*(\d+)',
                r'BBEE\s*level\s*(\d+)',
                r'B-BBEE\s*(\d+)',
                r'BBEE\s*(\d+)'
            ]

            for pattern in bbee_patterns:
                bbee_match = re.search(pattern, contractor_text, re.IGNORECASE)
                if bbee_match:
                    bbee_level = bbee_match.group(1)
                    contractor_name = contractor_text.replace(bbee_match.group(0), '').strip()
                    return contractor_name, bbee_level

            return contractor_text, None

        except Exception as e:
            logger.warning(f"Error extracting B-BBEE level: {e}")
            return contractor_text, None
    
    def parse_date(self, date_str: str) -> Optional[datetime]:
        """Parse date string to datetime object"""
        try:
            if not date_str or date_str.strip() == "" or date_str.strip().upper() == "DATE AWARDED":
                return None
            
            date_str = date_str.strip()
            
            # Common date patterns
            patterns = [
                r'(\d{1,2})\s+(\w+)\s+(\d{4})',  # "09 Jul 2025"
                r'(\d{1,2})/(\d{1,2})/(\d{4})',  # "09/07/2025"
                r'(\d{4})-(\d{1,2})-(\d{1,2})',  # "2025-07-09"
            ]
            
            month_map = {
                'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
                'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12,
                'January': 1, 'February': 2, 'March': 3, 'April': 4, 'May': 5, 'June': 6,
                'July': 7, 'August': 8, 'September': 9, 'October': 10, 'November': 11, 'December': 12
            }
            
            for pattern in patterns:
                match = re.search(pattern, date_str, re.IGNORECASE)
                if match:
                    if len(match.groups()) == 3:
                        if pattern.startswith(r'(\d{4})'):  # YYYY-MM-DD format
                            year = int(match.group(1))
                            month = int(match.group(2))
                            day = int(match.group(3))
                        elif '/' in pattern:  # DD/MM/YYYY format
                            day = int(match.group(1))
                            month = int(match.group(2))
                            year = int(match.group(3))
                        else:  # DD MMM YYYY format
                            day = int(match.group(1))
                            month_str = match.group(2)
                            year = int(match.group(3))
                            month = month_map.get(month_str, month_map.get(month_str.lower()))
                            if not month:
                                continue
                        
                        return datetime(year, month, day)
            
            return None
            
        except Exception as e:
            logger.warning(f"Error parsing date '{date_str}': {e}")
            return None
    
    async def scrape_current_awarded_tenders(self, crawler: AsyncWebCrawler) -> List[Dict]:
        """Scrape current awarded tenders from main tenders page"""
        try:
            logger.info("Scraping current awarded tenders...")
            
            # Extract awarded tenders data using CSS selectors
            extraction_schema = {
                "name": "Awarded Tenders",
                "baseSelector": "#pills-awarded table tbody tr",
                "fields": [
                    {"name": "bid_number", "selector": "td:nth-child(1)", "type": "text"},
                    {"name": "date_awarded", "selector": "td:nth-child(2)", "type": "text"},
                    {"name": "service_description", "selector": "td:nth-child(3)", "type": "text"},
                    {"name": "contractor", "selector": "td:nth-child(4)", "type": "text"}
                ]
            }
            
            extraction_strategy = JsonCssExtractionStrategy(extraction_schema, verbose=False)
            
            config = CrawlerRunConfig(
                extraction_strategy=extraction_strategy,
                cache_mode=CacheMode.BYPASS,
                js_code=[
                    "document.querySelector('[href=\"#pills-awarded\"]')?.click();",
                    "await new Promise(resolve => setTimeout(resolve, 3000));"
                ]
            )
            
            result = await crawler.arun(url=f"{PUBLIC_WORKS_BASE_URL}/tenders.html", config=config)
            
            if result.success and result.extracted_content:
                awarded_tenders = json.loads(result.extracted_content)
                
                # Process and clean the data
                processed_tenders = []
                for tender in awarded_tenders:
                    if tender.get('bid_number') and tender.get('bid_number').strip():
                        # Parse contract value and B-BBEE level
                        service_desc = tender.get('service_description', '')
                        contract_value, contract_value_text = self.parse_contract_value(service_desc)
                        
                        contractor_text = tender.get('contractor', '')
                        contractor_name, bbee_level = self.extract_bbee_level(contractor_text)
                        
                        # Parse date
                        date_awarded = self.parse_date(tender.get('date_awarded', ''))
                        
                        processed_tender = {
                            'bid_number': tender.get('bid_number', '').strip(),
                            'date_awarded': date_awarded.date().isoformat() if date_awarded else None,
                            'service_description': service_desc,
                            'contractor_name': contractor_name,
                            'contract_value': contract_value,
                            'contract_value_text': contract_value_text,
                            'bbee_level': bbee_level,
                            'data_source': 'current',
                            'archive_year': str(datetime.now().year),
                            'scraped_at': datetime.utcnow().isoformat() + "Z",
                            'run_id': self.run_id
                        }
                        
                        processed_tenders.append(processed_tender)
                
                logger.info(f"Scraped {len(processed_tenders)} current awarded tenders")
                return processed_tenders
            
            return []
            
        except Exception as e:
            logger.error(f"Error scraping current awarded tenders: {str(e)}")
            return []
    
    async def save_to_supabase(self, awarded_tenders: List[Dict]):
        """Save awarded tenders to Supabase"""
        if not awarded_tenders:
            return
        
        try:
            logger.info(f"Saving {len(awarded_tenders)} awarded tenders to Supabase...")
            
            # Batch upsert
            batch_size = 50
            total_saved = 0
            
            for i in range(0, len(awarded_tenders), batch_size):
                batch = awarded_tenders[i:i+batch_size]
                
                try:
                    result = supabase.table('awarded_tenders').upsert(
                        batch,
                        on_conflict='bid_number'
                    ).execute()
                    
                    if hasattr(result, 'error') and result.error:
                        logger.error(f"Supabase upsert error: {result.error}")
                    else:
                        total_saved += len(batch)
                        logger.info(f"✅ Saved awarded tenders batch {i//batch_size + 1}")
                        
                except Exception as batch_error:
                    logger.error(f"Error saving awarded tenders batch: {batch_error}")
            
            logger.info(f"✅ Saved {total_saved}/{len(awarded_tenders)} awarded tenders")
            
        except Exception as e:
            logger.error(f"Error saving awarded tenders: {str(e)}")
    
    async def run_scrape(self):
        """Run scraping process for awarded tenders"""
        start_time = datetime.utcnow()
        
        try:
            logger.info("🚀 Starting Public Works awarded tenders scraping...")
            
            async with AsyncWebCrawler(config=self.browser_config) as crawler:
                # Scrape current awarded tenders
                current_awarded = await self.scrape_current_awarded_tenders(crawler)
                
                # Save immediately
                if current_awarded:
                    await self.save_to_supabase(current_awarded)
            
            # Calculate final statistics
            end_time = datetime.utcnow()
            duration = (end_time - start_time).total_seconds()
            
            # Log final results
            logger.info(f"""
🎯 Scraping completed in {duration:.2f} seconds
Run ID: {self.run_id}
Current Awarded Tenders: {len(current_awarded)}
            """)
            
            return {
                'run_id': self.run_id,
                'current_awarded_tenders': len(current_awarded),
                'duration_seconds': duration
            }
            
        except Exception as e:
            logger.error(f"Critical error during scraping: {str(e)}")
            raise

async def main():
    """Main entry point"""
    scraper = PublicWorksSimpleScraper()
    
    try:
        result = await scraper.run_scrape()
        print(f"\n✅ Scraping completed successfully!")
        print(f"📊 Results: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Scraping failed: {str(e)}")
        print(f"\n❌ Scraping failed: {str(e)}")
        return None

if __name__ == "__main__":
    print("🏛️ PUBLIC WORKS AWARDED TENDERS SIMPLE SCRAPER")
    print("=" * 50)
    print("Scraping current awarded tenders from: http://www.publicworks.gov.za")
    print()
    
    asyncio.run(main())
