#!/usr/bin/env python3
"""
Show Public Works Quotations Results
Display summary of scraped quotations data
"""

import os
import pandas as pd
from datetime import datetime, timedelta
from supabase import create_client, Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

def show_results():
    """Show comprehensive results of the quotations scraping"""
    
    print("🏛️ PUBLIC WORKS QUOTATIONS SCRAPING RESULTS")
    print("=" * 60)
    
    try:
        # Get total count using direct query
        from supabase import create_client

        # Use a simple select to get count
        result = supabase.table('quotations').select('id').execute()
        total_count = len(result.data) if result.data else 0
        
        print(f"📊 Total Quotations Scraped: {total_count:,}")
        
        # Get sample data
        sample_result = supabase.table('quotations').select('*').limit(5).execute()
        
        if sample_result.data:
            df = pd.DataFrame(sample_result.data)
            
            # Convert dates
            if 'closing_date' in df.columns:
                df['closing_date'] = pd.to_datetime(df['closing_date'])
            if 'scraped_at' in df.columns:
                df['scraped_at'] = pd.to_datetime(df['scraped_at'])
            
            print(f"\n📈 Data Quality:")
            print(f"   • Quotation Numbers: {df['quotation_number'].notna().sum()}/{len(df)} complete")
            print(f"   • Descriptions: {df['description'].notna().sum()}/{len(df)} complete")
            print(f"   • Closing Dates: {df['closing_date'].notna().sum()}/{len(df)} complete")
            
            # Show date range
            if 'closing_date' in df.columns and not df['closing_date'].isna().all():
                min_date = df['closing_date'].min()
                max_date = df['closing_date'].max()
                print(f"\n📅 Date Range:")
                print(f"   • Earliest Closing: {min_date.strftime('%Y-%m-%d') if pd.notna(min_date) else 'N/A'}")
                print(f"   • Latest Closing: {max_date.strftime('%Y-%m-%d') if pd.notna(max_date) else 'N/A'}")
            
            # Show recent quotations
            print(f"\n🔍 Sample Recent Quotations:")
            print("-" * 60)
            
            for idx, row in df.head(3).iterrows():
                print(f"\n📋 {row.get('quotation_number', 'N/A')}")
                
                # Description (truncated)
                desc = str(row.get('description', 'N/A'))
                if len(desc) > 80:
                    desc = desc[:80] + "..."
                print(f"   Description: {desc}")
                
                # Closing date
                closing_date = row.get('closing_date_time', 'N/A')
                print(f"   Closing: {closing_date}")
        
        # Check for quotations closing soon
        print(f"\n⏰ Quotations Closing Soon:")
        print("-" * 60)
        
        now = datetime.now()
        future_date = now + timedelta(days=30)
        
        closing_soon_result = supabase.table('quotations').select('quotation_number, description, closing_date_time, closing_date').gte('closing_date', now.isoformat()).lte('closing_date', future_date.isoformat()).limit(5).execute()
        
        if closing_soon_result.data:
            closing_soon_df = pd.DataFrame(closing_soon_result.data)
            closing_soon_df['closing_date'] = pd.to_datetime(closing_soon_df['closing_date'])
            
            print(f"Found {len(closing_soon_df)} quotations closing in the next 30 days:")
            
            for idx, row in closing_soon_df.iterrows():
                closing_date = row['closing_date']
                days_left = (closing_date - now).days
                
                print(f"\n🔔 {row.get('quotation_number', 'N/A')} - {days_left} days left")
                print(f"   Closes: {closing_date.strftime('%Y-%m-%d %H:%M')}")
        else:
            print("No quotations closing in the next 30 days")
        
        # Database schema info
        print(f"\n🗃️ Database Schema:")
        print("-" * 60)
        print("✅ quotations table created with columns:")
        print("   • id (Primary Key)")
        print("   • quotation_number (Unique)")
        print("   • description")
        print("   • submission_at")
        print("   • closing_date_time")
        print("   • closing_date (Parsed timestamp)")
        print("   • invitation_links (Array)")
        print("   • status")
        print("   • scraped_at")
        print("   • run_id")
        
        # Success message
        print(f"\n✅ SUCCESS!")
        print("=" * 60)
        print("🎯 Public Works quotations scraping completed successfully!")
        print(f"📊 Database: {total_count:,} quotations stored in Supabase")
        print("🔄 Data is automatically updated when scraper runs")
        print("📱 Use quotations_dashboard.py for interactive analysis")
        print("🔍 Use public_works_quotations_scraper.py to update data")
        
        # Next steps
        print(f"\n📋 Next Steps:")
        print("1. Run the dashboard: python3 quotations_dashboard.py")
        print("2. Schedule regular scraping for updates")
        print("3. Set up alerts for quotations closing soon")
        print("4. Export data: Use dashboard option 5")
        
    except Exception as e:
        print(f"❌ Error retrieving results: {e}")

def create_count_function():
    """Create a count function in Supabase for better performance"""
    try:
        # This would need to be run in Supabase SQL editor
        sql = """
        CREATE OR REPLACE FUNCTION count_quotations()
        RETURNS INTEGER AS $$
        BEGIN
            RETURN (SELECT COUNT(*) FROM quotations);
        END;
        $$ LANGUAGE plpgsql;
        """
        print("SQL function to create in Supabase:")
        print(sql)
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    show_results()
