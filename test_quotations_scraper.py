#!/usr/bin/env python3
"""
Test Public Works Quotations Scraper
Quick test to verify the scraper functionality
"""

import asyncio
import json
from public_works_quotations_scraper import PublicWorksQuotationsScraper
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_scraper():
    """Test the quotations scraper"""
    print("🧪 TESTING PUBLIC WORKS QUOTATIONS SCRAPER")
    print("=" * 50)
    
    scraper = PublicWorksQuotationsScraper()
    
    try:
        # Test the scraping functionality
        result = await scraper.run_full_scrape()
        
        if result:
            print("\n✅ TEST PASSED!")
            print(f"📊 Scraping Results:")
            print(f"   - Run ID: {result['run_id']}")
            print(f"   - Status: {result['status']}")
            print(f"   - Total Quotations: {result['total_quotations']}")
            print(f"   - New Quotations: {result['new_quotations']}")
            print(f"   - Duration: {result['duration_seconds']:.2f} seconds")
            
            if result['errors']:
                print(f"   - Errors: {len(result['errors'])}")
                for error in result['errors']:
                    print(f"     • {error}")
            else:
                print("   - No errors encountered")
                
        else:
            print("\n❌ TEST FAILED!")
            print("No results returned from scraper")
            
    except Exception as e:
        print(f"\n❌ TEST FAILED!")
        print(f"Error: {e}")
        logger.exception("Test failed with exception")

async def test_date_parsing():
    """Test the date parsing functionality"""
    print("\n🗓️ TESTING DATE PARSING")
    print("-" * 30)
    
    scraper = PublicWorksQuotationsScraper()
    
    test_dates = [
        "29 Jul 2025 @ 11h00am",
        "23 Jul 2025 @ 11h00am", 
        "21 Jul 2025 @ 11h00am",
        "17 Jul 2025 @ 11h00am",
        "16 Jul 2025 @ 11h00am",
        "15 Jul 2025 @ 11h00am",
        "14 Jul 2025 @ 11h00am",
        "11 Jul 2025 @ 11h00am",
        "10 Jul 2025 @ 11h00am",
        "Invalid date string",
        "",
        None
    ]
    
    for date_str in test_dates:
        try:
            parsed_date = scraper.parse_closing_date(date_str)
            if parsed_date:
                print(f"✅ '{date_str}' → {parsed_date}")
            else:
                print(f"❌ '{date_str}' → None")
        except Exception as e:
            print(f"❌ '{date_str}' → Error: {e}")

async def test_link_extraction():
    """Test the link extraction functionality"""
    print("\n🔗 TESTING LINK EXTRACTION")
    print("-" * 30)
    
    scraper = PublicWorksQuotationsScraper()
    
    test_html = '''
    <a href="PDFs/quotations/Quotation_Doc_GQEQ-2025-2026-059.pdf">Invitation</a>
    <a href="/PDFs/quotations/Quotation_Doc_GQEQ-2025-2026-060.pdf">Document</a>
    <a href="http://www.publicworks.gov.za/PDFs/quotations/test.pdf">Full URL</a>
    '''
    
    links = scraper.extract_invitation_links(test_html)
    print(f"Extracted {len(links)} links:")
    for link in links:
        print(f"  • {link}")

def main():
    """Main test execution"""
    print("🚀 STARTING QUOTATIONS SCRAPER TESTS")
    print("=" * 60)
    
    # Run all tests
    asyncio.run(test_date_parsing())
    asyncio.run(test_link_extraction())
    asyncio.run(test_scraper())
    
    print("\n🎯 ALL TESTS COMPLETED")
    print("=" * 60)

if __name__ == "__main__":
    main()
