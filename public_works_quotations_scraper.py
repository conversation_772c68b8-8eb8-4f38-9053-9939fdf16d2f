#!/usr/bin/env python3
"""
Public Works Quotations Scraper
Scrapes quotation data from http://www.publicworks.gov.za/quotations.html
and stores in Supabase database
"""

import asyncio
import json
import os
import uuid
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging
from dataclasses import dataclass
import re

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
from supabase import create_client, Client
from dotenv import load_dotenv
import pandas as pd

# Load environment variables
load_dotenv()

# Configuration
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')
PUBLIC_WORKS_URL = "http://www.publicworks.gov.za/quotations.html#gsc.tab=0"

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('public_works_quotations_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ScrapingStats:
    """Track scraping statistics"""
    run_id: str
    total_quotations: int = 0
    new_quotations: int = 0
    updated_quotations: int = 0
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []

class PublicWorksQuotationsScraper:
    """Public Works Quotations Scraper"""
    
    def __init__(self):
        self.stats = ScrapingStats(run_id=str(uuid.uuid4()))
        self.browser_config = BrowserConfig(
            headless=True,
            verbose=False,
            java_script_enabled=True,
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        )
        
        # CSS extraction schema for quotation data
        self.extraction_schema = {
            "name": "Public Works Quotations",
            "baseSelector": "table tbody tr",
            "fields": [
                {
                    "name": "quotation_number",
                    "selector": "td:nth-child(1)",
                    "type": "text"
                },
                {
                    "name": "description",
                    "selector": "td:nth-child(2)",
                    "type": "text"
                },
                {
                    "name": "submission_at",
                    "selector": "td:nth-child(3)",
                    "type": "text"
                },
                {
                    "name": "closing_date_time",
                    "selector": "td:nth-child(4)",
                    "type": "text"
                }
            ]
        }
    
    async def create_quotations_table(self):
        """Create the quotations table in Supabase if it doesn't exist"""
        try:
            logger.info("Creating quotations table in Supabase...")
            
            # Check if table exists by trying to query it
            try:
                result = supabase.table('quotations').select('*').limit(1).execute()
                logger.info("Quotations table already exists")
                return True
            except Exception:
                logger.info("Quotations table doesn't exist, creating it...")
            
            # Create table using SQL
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS quotations (
                id SERIAL PRIMARY KEY,
                quotation_number TEXT UNIQUE,
                description TEXT,
                submission_at TEXT,
                closing_date_time TEXT,
                closing_date TIMESTAMPTZ,
                invitation_links TEXT[],
                status TEXT DEFAULT 'active',
                scraped_at TIMESTAMPTZ DEFAULT NOW(),
                updated_at TIMESTAMPTZ DEFAULT NOW(),
                run_id TEXT
            );
            
            -- Create indexes for better performance
            CREATE INDEX IF NOT EXISTS idx_quotations_number ON quotations(quotation_number);
            CREATE INDEX IF NOT EXISTS idx_quotations_closing_date ON quotations(closing_date);
            CREATE INDEX IF NOT EXISTS idx_quotations_status ON quotations(status);
            CREATE INDEX IF NOT EXISTS idx_quotations_scraped_at ON quotations(scraped_at);
            """
            
            # Execute the SQL using RPC (if available) or direct SQL execution
            # Note: This might need to be done manually in Supabase dashboard
            logger.info("Table creation SQL prepared. You may need to run this manually in Supabase:")
            logger.info(create_table_sql)
            
            return True
            
        except Exception as e:
            logger.error(f"Error creating quotations table: {str(e)}")
            return False
    
    async def initialize_scraping_run(self) -> str:
        """Initialize a new scraping run in the database"""
        try:
            # Create scraping_runs table if it doesn't exist
            create_runs_table_sql = """
            CREATE TABLE IF NOT EXISTS scraping_runs (
                id SERIAL PRIMARY KEY,
                run_id TEXT UNIQUE,
                status TEXT,
                scraper_type TEXT DEFAULT 'public_works_quotations',
                scraper_version TEXT DEFAULT '1.0.0',
                start_time TIMESTAMPTZ DEFAULT NOW(),
                end_time TIMESTAMPTZ,
                total_quotations_found INTEGER DEFAULT 0,
                new_quotations_added INTEGER DEFAULT 0,
                updated_quotations_count INTEGER DEFAULT 0,
                errors_encountered TEXT[],
                user_agent TEXT,
                notes TEXT
            );
            """
            
            logger.info("Scraping runs table SQL prepared")
            
            # Try to insert the run record
            result = supabase.table('scraping_runs').insert({
                'run_id': self.stats.run_id,
                'status': 'started',
                'scraper_type': 'public_works_quotations',
                'scraper_version': '1.0.0',
                'user_agent': self.browser_config.user_agent,
                'notes': 'Public Works quotations scraper'
            }).execute()
            
            logger.info(f"🚀 Initialized quotations scraping run: {self.stats.run_id}")
            return self.stats.run_id
            
        except Exception as e:
            logger.error(f"Failed to initialize scraping run: {str(e)}")
            # Continue anyway
            return self.stats.run_id
    
    def parse_closing_date(self, closing_date_str: str) -> Optional[datetime]:
        """Parse closing date string to datetime object"""
        try:
            if not closing_date_str or closing_date_str.strip() == "":
                return None
            
            # Clean the string
            date_str = closing_date_str.strip()
            
            # Common patterns in the data
            patterns = [
                r'(\d{1,2})\s+(\w+)\s+(\d{4})\s+@\s+(\d{1,2})h(\d{2})(?:am|pm)?',  # "29 Jul 2025 @ 11h00am"
                r'(\d{1,2})\s+(\w+)\s+(\d{4})\s+@\s+(\d{1,2})h(\d{2})',  # "29 Jul 2025 @ 11h00"
                r'(\d{1,2})\s+(\w+)\s+(\d{4})',  # "29 Jul 2025"
            ]
            
            month_map = {
                'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
                'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12,
                'January': 1, 'February': 2, 'March': 3, 'April': 4, 'May': 5, 'June': 6,
                'July': 7, 'August': 8, 'September': 9, 'October': 10, 'November': 11, 'December': 12
            }
            
            for pattern in patterns:
                match = re.search(pattern, date_str, re.IGNORECASE)
                if match:
                    day = int(match.group(1))
                    month_str = match.group(2)
                    year = int(match.group(3))
                    
                    month = month_map.get(month_str, month_map.get(month_str.lower()))
                    if not month:
                        continue
                    
                    # Default time
                    hour = 11
                    minute = 0
                    
                    if len(match.groups()) >= 5:
                        hour = int(match.group(4))
                        minute = int(match.group(5))
                    
                    return datetime(year, month, day, hour, minute)
            
            logger.warning(f"Could not parse date: {date_str}")
            return None
            
        except Exception as e:
            logger.warning(f"Error parsing date '{closing_date_str}': {e}")
            return None
    
    def extract_invitation_links(self, description_html: str) -> List[str]:
        """Extract invitation/document links from description HTML"""
        try:
            import re
            
            # Find all PDF links
            pdf_links = re.findall(r'href=["\']([^"\']*\.pdf)["\']', description_html, re.IGNORECASE)
            
            # Clean and make absolute URLs
            base_url = "http://www.publicworks.gov.za/"
            cleaned_links = []
            
            for link in pdf_links:
                if link.startswith('http'):
                    cleaned_links.append(link)
                elif link.startswith('/'):
                    cleaned_links.append(base_url + link[1:])
                else:
                    cleaned_links.append(base_url + link)
            
            return cleaned_links
            
        except Exception as e:
            logger.warning(f"Error extracting links: {e}")
            return []

    async def scrape_quotations_page(self, crawler: AsyncWebCrawler) -> List[Dict]:
        """Scrape quotations from the main page"""
        try:
            logger.info("Scraping quotations from Public Works website...")

            extraction_strategy = JsonCssExtractionStrategy(self.extraction_schema, verbose=False)

            config = CrawlerRunConfig(
                extraction_strategy=extraction_strategy,
                cache_mode=CacheMode.BYPASS,
                wait_for="css:table tbody tr",
                js_code=["window.scrollTo(0, document.body.scrollHeight);"]
            )

            result = await crawler.arun(url=PUBLIC_WORKS_URL, config=config)

            if not result.success:
                logger.error(f"Failed to scrape quotations page: {result.error_message}")
                self.stats.errors.append(f"Page scraping failed: {result.error_message}")
                return []

            # Parse extracted data
            quotations = json.loads(result.extracted_content) if result.extracted_content else []

            # Clean and validate data
            cleaned_quotations = []
            for quotation in quotations:
                if quotation.get('quotation_number') and quotation.get('quotation_number').strip():
                    # Clean all text fields
                    cleaned_quotation = {
                        key: value.strip() if isinstance(value, str) else value
                        for key, value in quotation.items()
                    }

                    # Parse closing date
                    closing_date = self.parse_closing_date(cleaned_quotation.get('closing_date_time', ''))
                    cleaned_quotation['closing_date'] = closing_date.isoformat() if closing_date else None

                    # Extract invitation links
                    description = cleaned_quotation.get('description', '')
                    cleaned_quotation['invitation_links'] = self.extract_invitation_links(description)

                    # Add metadata
                    cleaned_quotation['scraped_at'] = datetime.utcnow().isoformat() + "Z"
                    cleaned_quotation['run_id'] = self.stats.run_id
                    cleaned_quotation['status'] = 'active'

                    cleaned_quotations.append(cleaned_quotation)

            logger.info(f"Scraped {len(cleaned_quotations)} quotations from main page")
            return cleaned_quotations

        except Exception as e:
            logger.error(f"Error scraping quotations page: {str(e)}")
            self.stats.errors.append(f"Scraping error: {str(e)}")
            return []

    async def save_quotations_to_supabase(self, quotations: List[Dict]):
        """Save quotations to Supabase with upsert logic"""
        if not quotations:
            return

        try:
            logger.info(f"Saving {len(quotations)} quotations to Supabase...")

            # Batch upsert (insert or update based on quotation_number)
            batch_size = 50
            total_saved = 0

            for i in range(0, len(quotations), batch_size):
                batch = quotations[i:i+batch_size]

                try:
                    # Use upsert to handle duplicates
                    result = supabase.table('quotations').upsert(
                        batch,
                        on_conflict='quotation_number'
                    ).execute()

                    if hasattr(result, 'error') and result.error:
                        logger.error(f"Supabase upsert error: {result.error}")
                        self.stats.errors.append(f"Database error: {result.error}")
                    else:
                        total_saved += len(batch)
                        logger.info(f"✅ Saved batch {i//batch_size + 1} ({len(batch)} quotations)")

                except Exception as batch_error:
                    logger.error(f"Error saving batch {i//batch_size + 1}: {batch_error}")
                    self.stats.errors.append(f"Batch save error: {batch_error}")

            self.stats.total_quotations = len(quotations)
            self.stats.new_quotations = total_saved  # Approximate

            logger.info(f"✅ Successfully saved {total_saved}/{len(quotations)} quotations to Supabase")

        except Exception as e:
            logger.error(f"Error saving to Supabase: {str(e)}")
            self.stats.errors.append(f"Database save error: {str(e)}")

    async def update_scraping_run(self, status: str, end_time: Optional[datetime] = None):
        """Update scraping run status and statistics"""
        try:
            update_data = {
                'status': status,
                'total_quotations_found': self.stats.total_quotations,
                'new_quotations_added': self.stats.new_quotations,
                'updated_quotations_count': self.stats.updated_quotations,
                'errors_encountered': self.stats.errors
            }

            if end_time:
                update_data['end_time'] = end_time.isoformat()

            supabase.table('scraping_runs').update(update_data).eq('run_id', self.stats.run_id).execute()
            logger.info(f"Updated scraping run {self.stats.run_id} with status: {status}")

        except Exception as e:
            logger.error(f"Failed to update scraping run: {str(e)}")

    async def run_full_scrape(self):
        """Run complete scraping process"""
        start_time = datetime.utcnow()

        try:
            # Initialize database and scraping run
            await self.create_quotations_table()
            await self.initialize_scraping_run()

            logger.info("🚀 Starting Public Works quotations scraping...")

            async with AsyncWebCrawler(config=self.browser_config) as crawler:
                # Scrape quotations
                quotations = await self.scrape_quotations_page(crawler)

                if quotations:
                    # Save to database
                    await self.save_quotations_to_supabase(quotations)
                else:
                    logger.warning("No quotations were scraped")

            # Update final statistics
            end_time = datetime.utcnow()

            if self.stats.errors:
                status = 'partial' if self.stats.total_quotations > 0 else 'failed'
            else:
                status = 'completed'

            await self.update_scraping_run(status, end_time)

            # Log final results
            duration = (end_time - start_time).total_seconds()
            logger.info(f"""
🎯 Scraping completed in {duration:.2f} seconds
Run ID: {self.stats.run_id}
Status: {status}
Total Quotations: {self.stats.total_quotations}
New Quotations: {self.stats.new_quotations}
Errors: {len(self.stats.errors)}
            """)

            if self.stats.errors:
                logger.warning(f"Errors encountered: {self.stats.errors}")

            return {
                'run_id': self.stats.run_id,
                'status': status,
                'total_quotations': self.stats.total_quotations,
                'new_quotations': self.stats.new_quotations,
                'duration_seconds': duration,
                'errors': self.stats.errors
            }

        except Exception as e:
            logger.error(f"Critical error during scraping: {str(e)}")
            await self.update_scraping_run('failed', datetime.utcnow())
            raise

async def main():
    """Main entry point"""
    scraper = PublicWorksQuotationsScraper()

    try:
        result = await scraper.run_full_scrape()
        print(f"\n✅ Scraping completed successfully!")
        print(f"📊 Results: {result}")
        return result

    except Exception as e:
        logger.error(f"Scraping failed: {str(e)}")
        print(f"\n❌ Scraping failed: {str(e)}")
        return None

if __name__ == "__main__":
    print("🏛️ PUBLIC WORKS QUOTATIONS SCRAPER")
    print("=" * 50)
    print("Scraping quotations from: http://www.publicworks.gov.za/quotations.html")
    print()

    asyncio.run(main())
