#!/usr/bin/env python3
"""
Simple CIDB Screenshot Scraper
Fast and reliable screenshot capture of CIDB portal pages
"""

import asyncio
import os
import time
from datetime import datetime
from pathlib import Path
import base64
from dotenv import load_dotenv
from firecrawl import FirecrawlApp

# Load environment variables
load_dotenv()

def create_screenshots_directory():
    """Create the screenshots directory in Documents"""
    documents_path = Path.home() / "Documents"
    screenshots_dir = documents_path / "cidb_screenshots"
    screenshots_dir.mkdir(exist_ok=True)
    return screenshots_dir

def capture_with_firecrawl():
    """Use FireCrawl to capture screenshots"""
    print("🚀 CIDB SCREENSHOT CAPTURE WITH FIRECRAWL")
    print("=" * 50)
    
    # Initialize FireCrawl
    firecrawl_app = FirecrawlApp(api_key=os.getenv('FIRECRAWL_API_KEY'))
    
    # Create screenshots directory
    screenshots_dir = create_screenshots_directory()
    print(f"📁 Screenshots directory: {screenshots_dir}")
    
    # CIDB URLs to capture
    urls_to_capture = [
        {
            "name": "CIDB_Main_Portal",
            "url": "https://portal.cidb.org.za/RegisterOfContractors/",
            "description": "Main CIDB contractor portal"
        },
        {
            "name": "CIDB_Alternative_Portal", 
            "url": "https://registers.cidb.org.za/",
            "description": "Alternative CIDB registers"
        },
        {
            "name": "CIDB_Public_Search",
            "url": "https://registers.cidb.org.za/PublicContractors/ContractorSearch",
            "description": "Public contractor search"
        },
        {
            "name": "CIDB_Main_Website",
            "url": "https://www.cidb.org.za/",
            "description": "Main CIDB website"
        },
        {
            "name": "CIDB_Resource_Centre",
            "url": "https://www.cidb.org.za/resource-centre/register-of-contractors/",
            "description": "CIDB resource centre"
        }
    ]
    
    screenshots_captured = 0
    total_size = 0
    
    for i, url_info in enumerate(urls_to_capture, 1):
        try:
            print(f"\n📸 Capturing {i}/{len(urls_to_capture)}: {url_info['name']}")
            print(f"   🔗 URL: {url_info['url']}")
            print(f"   📄 Description: {url_info['description']}")
            
            # Use FireCrawl to scrape with screenshot
            result = firecrawl_app.scrape_url(url_info['url'], {
                "formats": ["screenshot"],
                "onlyMainContent": False,
                "waitFor": 3000  # Wait 3 seconds
            })
            
            if result and result.get('screenshot'):
                # Generate filename
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{url_info['name']}_{timestamp}.png"
                screenshot_path = screenshots_dir / filename
                
                # Save screenshot
                screenshot_data = base64.b64decode(result['screenshot'])
                with open(screenshot_path, 'wb') as f:
                    f.write(screenshot_data)
                
                # Get file info
                file_size = len(screenshot_data)
                total_size += file_size
                
                print(f"   ✅ Screenshot saved: {filename}")
                print(f"   📊 File size: {file_size:,} bytes")
                print(f"   📁 Path: {screenshot_path}")
                
                screenshots_captured += 1
            else:
                print(f"   ❌ Failed to capture screenshot for {url_info['name']}")
            
            # Rate limiting
            time.sleep(2)
            
        except Exception as e:
            print(f"   ❌ Error capturing {url_info['name']}: {e}")
            continue
    
    # Capture pagination screenshots of main portal
    print(f"\n📄 CAPTURING PAGINATION SCREENSHOTS")
    print("-" * 40)
    
    main_portal_base = "https://portal.cidb.org.za/RegisterOfContractors/"
    
    for page_num in range(1, 11):  # Pages 1-10
        try:
            print(f"\n📸 Capturing page {page_num}/10")
            
            # Try different URL patterns for pagination
            page_urls = [
                f"{main_portal_base}?page={page_num}",
                f"{main_portal_base}#page={page_num}",
                main_portal_base  # For page 1, just use base URL
            ]
            
            for page_url in page_urls:
                try:
                    result = firecrawl_app.scrape_url(page_url, {
                        "formats": ["screenshot"],
                        "onlyMainContent": False,
                        "waitFor": 3000
                    })
                    
                    if result and result.get('screenshot'):
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        filename = f"CIDB_Portal_Page_{page_num:02d}_{timestamp}.png"
                        screenshot_path = screenshots_dir / filename
                        
                        screenshot_data = base64.b64decode(result['screenshot'])
                        with open(screenshot_path, 'wb') as f:
                            f.write(screenshot_data)
                        
                        file_size = len(screenshot_data)
                        total_size += file_size
                        
                        print(f"   ✅ Page {page_num} captured: {filename}")
                        print(f"   📊 File size: {file_size:,} bytes")
                        
                        screenshots_captured += 1
                        break  # Success, move to next page
                        
                except Exception as e:
                    print(f"   ⚠️ Failed URL {page_url}: {e}")
                    continue
            
            time.sleep(2)  # Rate limiting
            
        except Exception as e:
            print(f"   ❌ Error capturing page {page_num}: {e}")
            continue
    
    # Final summary
    print(f"\n🎯 SCREENSHOT CAPTURE COMPLETED")
    print("=" * 50)
    print(f"📸 Total screenshots captured: {screenshots_captured}")
    print(f"📁 Saved to: {screenshots_dir}")
    print(f"💾 Total size: {total_size:,} bytes ({total_size/1024/1024:.2f} MB)")
    
    # List all captured files
    screenshot_files = list(screenshots_dir.glob("*.png"))
    screenshot_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
    
    if screenshot_files:
        print(f"\n📋 CAPTURED SCREENSHOTS:")
        print("-" * 30)
        
        for i, screenshot_file in enumerate(screenshot_files, 1):
            file_size = screenshot_file.stat().st_size
            created_time = datetime.fromtimestamp(screenshot_file.stat().st_mtime)
            
            print(f"   {i:2d}. {screenshot_file.name}")
            print(f"       📊 Size: {file_size:,} bytes")
            print(f"       🕐 Created: {created_time.strftime('%H:%M:%S')}")
        
        print(f"\n✅ SUCCESS: All screenshots saved to your Documents folder!")
        print(f"📁 Open Finder and navigate to: Documents/cidb_screenshots")
        print(f"🖼️ You can now view all CIDB portal pages visually")
    else:
        print(f"\n❌ No screenshots were captured")
        print(f"💡 Check FireCrawl API key and internet connection")
    
    return {
        'screenshots_captured': screenshots_captured,
        'total_size_bytes': total_size,
        'output_directory': str(screenshots_dir),
        'files': [f.name for f in screenshot_files]
    }

def main():
    """Main execution function"""
    try:
        result = capture_with_firecrawl()
        
        if result['screenshots_captured'] > 0:
            print(f"\n🎉 MISSION ACCOMPLISHED!")
            print(f"📸 {result['screenshots_captured']} screenshots captured")
            print(f"📁 Location: {result['output_directory']}")
            print(f"💾 Size: {result['total_size_bytes']/1024/1024:.2f} MB")
            
            print(f"\n📋 WHAT YOU CAN DO NOW:")
            print(f"   1. Open Finder → Documents → cidb_screenshots")
            print(f"   2. View all CIDB portal pages visually")
            print(f"   3. Analyze page layouts and structures")
            print(f"   4. Use screenshots for manual data extraction")
            print(f"   5. Share screenshots for analysis")
        else:
            print(f"\n⚠️ No screenshots captured")
            print(f"💡 Check API keys and try again")
        
        return result
        
    except Exception as e:
        print(f"❌ Screenshot capture failed: {e}")
        return None

if __name__ == "__main__":
    main()
