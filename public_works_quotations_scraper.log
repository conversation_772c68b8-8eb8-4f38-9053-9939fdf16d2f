2025-07-10 20:01:20,645 - WARNING - Could not parse date: Invalid date string
2025-07-10 20:01:20,690 - INFO - Creating quotations table in Supabase...
2025-07-10 20:01:21,689 - INFO - HTTP Request: GET https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?select=%2A&limit=1 "HTTP/2 200 OK"
2025-07-10 20:01:21,692 - INFO - Quotations table already exists
2025-07-10 20:01:21,692 - INFO - Scraping runs table SQL prepared
2025-07-10 20:01:22,547 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs "HTTP/2 400 Bad Request"
2025-07-10 20:01:22,548 - ERROR - Failed to initialize scraping run: {'message': "Could not find the 'scraper_type' column of 'scraping_runs' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-07-10 20:01:22,548 - INFO - 🚀 Starting Public Works quotations scraping...
2025-07-10 20:01:23,612 - INFO - Scraping quotations from Public Works website...
2025-07-10 20:01:52,638 - WARNING - Could not parse date: 14 Jul 025 @ 11h00am
2025-07-10 20:01:52,638 - WARNING - Could not parse date: 10 Jul 025 @ 11h00am
2025-07-10 20:01:52,638 - WARNING - Could not parse date: 02 Jul 025 @ 11h00am
2025-07-10 20:01:52,639 - WARNING - Could not parse date: 02 Jul 025 @ 11h00am
2025-07-10 20:01:52,639 - WARNING - Could not parse date: 01 Jul 025 @ 11h00am
2025-07-10 20:01:52,639 - WARNING - Could not parse date: 01 Jul 025 @ 11h00am
2025-07-10 20:01:52,639 - WARNING - Could not parse date: 01 Jul 025 @ 11h00am
2025-07-10 20:01:52,648 - WARNING - Could not parse date: 12 feb 2025 @ 11h00am
2025-07-10 20:01:52,662 - WARNING - Could not parse date: 12 JuL 2024 @ 11h00am
2025-07-10 20:01:52,674 - WARNING - Could not parse date: Bid Box, National Department of Public Works and
                                                                        Infrastructure: 251 AVN Building, Cnr NANA SITA
                                                                        & Thabo
                                                                        Sehume Street, Pretoria
2025-07-10 20:01:52,674 - WARNING - Could not parse date: Bid Box, National Department of Public Works and
                                                                        Infrastructure: 251 AVN Building, Cnr NANA SITA
                                                                        & Thabo
                                                                        Sehume Street, Pretoria
2025-07-10 20:01:52,674 - WARNING - Could not parse date: Bid Box, National Department of Public Works and
                                                                        Infrastructure: 251 AVN Building, Cnr NANA SITA
                                                                        & Thabo
                                                                        Sehume Street, Pretoria
2025-07-10 20:01:52,703 - WARNING - Error parsing date '156 Jul 2022 @ 11h00am': day is out of range for month
2025-07-10 20:01:52,707 - INFO - Scraped 9826 quotations from main page
2025-07-10 20:01:52,714 - INFO - Saving 9826 quotations to Supabase...
2025-07-10 20:01:53,275 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22invitation_links%22%2C%22closing_date%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:01:53,283 - INFO - ✅ Saved batch 1 (50 quotations)
2025-07-10 20:01:53,706 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:01:53,714 - INFO - ✅ Saved batch 2 (50 quotations)
2025-07-10 20:01:54,065 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:01:54,067 - ERROR - Error saving batch 3: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:01:54,504 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:01:54,507 - INFO - ✅ Saved batch 4 (50 quotations)
2025-07-10 20:01:55,320 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:01:55,321 - INFO - ✅ Saved batch 5 (50 quotations)
2025-07-10 20:01:55,715 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:01:55,717 - INFO - ✅ Saved batch 6 (50 quotations)
2025-07-10 20:01:56,266 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:01:56,296 - INFO - ✅ Saved batch 7 (50 quotations)
2025-07-10 20:01:56,652 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:01:56,653 - ERROR - Error saving batch 8: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:01:57,065 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:01:57,068 - INFO - ✅ Saved batch 9 (50 quotations)
2025-07-10 20:01:57,473 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:01:57,476 - INFO - ✅ Saved batch 10 (50 quotations)
2025-07-10 20:01:57,791 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:01:57,797 - INFO - ✅ Saved batch 11 (50 quotations)
2025-07-10 20:01:58,115 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:01:58,117 - ERROR - Error saving batch 12: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:01:59,010 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:01:59,013 - INFO - ✅ Saved batch 13 (50 quotations)
2025-07-10 20:01:59,418 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:01:59,419 - ERROR - Error saving batch 14: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:01:59,830 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:01:59,833 - INFO - ✅ Saved batch 15 (50 quotations)
2025-07-10 20:02:00,177 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:00,188 - INFO - ✅ Saved batch 16 (50 quotations)
2025-07-10 20:02:00,506 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:00,508 - ERROR - Error saving batch 17: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:01,056 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:01,058 - INFO - ✅ Saved batch 18 (50 quotations)
2025-07-10 20:02:01,649 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:01,655 - INFO - ✅ Saved batch 19 (50 quotations)
2025-07-10 20:02:01,995 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:01,996 - INFO - ✅ Saved batch 20 (50 quotations)
2025-07-10 20:02:02,360 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:02,367 - INFO - ✅ Saved batch 21 (50 quotations)
2025-07-10 20:02:02,787 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:02,799 - INFO - ✅ Saved batch 22 (50 quotations)
2025-07-10 20:02:03,102 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:03,108 - INFO - ✅ Saved batch 23 (50 quotations)
2025-07-10 20:02:03,397 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:03,398 - ERROR - Error saving batch 24: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:03,719 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:03,725 - INFO - ✅ Saved batch 25 (50 quotations)
2025-07-10 20:02:04,027 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:04,029 - INFO - ✅ Saved batch 26 (50 quotations)
2025-07-10 20:02:04,415 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:04,416 - INFO - ✅ Saved batch 27 (50 quotations)
2025-07-10 20:02:04,826 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:04,828 - INFO - ✅ Saved batch 28 (50 quotations)
2025-07-10 20:02:05,142 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:05,148 - INFO - ✅ Saved batch 29 (50 quotations)
2025-07-10 20:02:05,482 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:05,485 - INFO - ✅ Saved batch 30 (50 quotations)
2025-07-10 20:02:05,776 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:05,776 - ERROR - Error saving batch 31: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:06,076 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:06,078 - ERROR - Error saving batch 32: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:06,376 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:06,377 - ERROR - Error saving batch 33: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:06,660 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:06,664 - INFO - ✅ Saved batch 34 (50 quotations)
2025-07-10 20:02:06,977 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:06,979 - ERROR - Error saving batch 35: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:07,282 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:07,288 - INFO - ✅ Saved batch 36 (50 quotations)
2025-07-10 20:02:07,645 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:07,650 - INFO - ✅ Saved batch 37 (50 quotations)
2025-07-10 20:02:08,077 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:08,077 - ERROR - Error saving batch 38: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:08,393 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:08,397 - INFO - ✅ Saved batch 39 (50 quotations)
2025-07-10 20:02:08,721 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:08,724 - INFO - ✅ Saved batch 40 (50 quotations)
2025-07-10 20:02:09,016 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:09,019 - INFO - ✅ Saved batch 41 (50 quotations)
2025-07-10 20:02:09,366 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:09,367 - ERROR - Error saving batch 42: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:09,711 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:09,715 - INFO - ✅ Saved batch 43 (50 quotations)
2025-07-10 20:02:10,036 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:10,037 - ERROR - Error saving batch 44: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:10,343 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:10,347 - INFO - ✅ Saved batch 45 (50 quotations)
2025-07-10 20:02:10,682 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:10,685 - INFO - ✅ Saved batch 46 (50 quotations)
2025-07-10 20:02:11,020 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:11,026 - INFO - ✅ Saved batch 47 (50 quotations)
2025-07-10 20:02:11,336 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:11,347 - INFO - ✅ Saved batch 48 (50 quotations)
2025-07-10 20:02:11,707 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:11,709 - INFO - ✅ Saved batch 49 (50 quotations)
2025-07-10 20:02:12,037 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:12,047 - INFO - ✅ Saved batch 50 (50 quotations)
2025-07-10 20:02:12,406 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:12,408 - ERROR - Error saving batch 51: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:12,721 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:12,727 - INFO - ✅ Saved batch 52 (50 quotations)
2025-07-10 20:02:13,082 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:13,087 - INFO - ✅ Saved batch 53 (50 quotations)
2025-07-10 20:02:13,432 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:13,436 - INFO - ✅ Saved batch 54 (50 quotations)
2025-07-10 20:02:13,774 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:13,775 - ERROR - Error saving batch 55: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:14,120 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:14,130 - INFO - ✅ Saved batch 56 (50 quotations)
2025-07-10 20:02:14,481 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:14,484 - INFO - ✅ Saved batch 57 (50 quotations)
2025-07-10 20:02:14,833 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:14,836 - INFO - ✅ Saved batch 58 (50 quotations)
2025-07-10 20:02:15,151 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:15,156 - INFO - ✅ Saved batch 59 (50 quotations)
2025-07-10 20:02:15,470 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:15,475 - INFO - ✅ Saved batch 60 (50 quotations)
2025-07-10 20:02:15,773 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:15,782 - INFO - ✅ Saved batch 61 (50 quotations)
2025-07-10 20:02:16,085 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:16,097 - INFO - ✅ Saved batch 62 (50 quotations)
2025-07-10 20:02:16,406 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:16,407 - ERROR - Error saving batch 63: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:16,825 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:16,827 - INFO - ✅ Saved batch 64 (50 quotations)
2025-07-10 20:02:17,138 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:17,149 - INFO - ✅ Saved batch 65 (50 quotations)
2025-07-10 20:02:17,543 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:17,546 - INFO - ✅ Saved batch 66 (50 quotations)
2025-07-10 20:02:17,953 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:17,955 - ERROR - Error saving batch 67: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:18,253 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:18,260 - INFO - ✅ Saved batch 68 (50 quotations)
2025-07-10 20:02:18,577 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:18,578 - ERROR - Error saving batch 69: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:18,987 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:18,989 - ERROR - Error saving batch 70: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:19,387 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:19,389 - INFO - ✅ Saved batch 71 (50 quotations)
2025-07-10 20:02:19,711 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:19,713 - ERROR - Error saving batch 72: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:20,014 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:20,014 - ERROR - Error saving batch 73: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:20,411 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:20,416 - INFO - ✅ Saved batch 74 (50 quotations)
2025-07-10 20:02:20,841 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:20,842 - ERROR - Error saving batch 75: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:21,281 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:21,287 - INFO - ✅ Saved batch 76 (50 quotations)
2025-07-10 20:02:21,718 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:21,721 - INFO - ✅ Saved batch 77 (50 quotations)
2025-07-10 20:02:22,053 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:22,058 - INFO - ✅ Saved batch 78 (50 quotations)
2025-07-10 20:02:22,372 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:22,376 - INFO - ✅ Saved batch 79 (50 quotations)
2025-07-10 20:02:22,712 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:22,716 - INFO - ✅ Saved batch 80 (50 quotations)
2025-07-10 20:02:23,022 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:23,028 - INFO - ✅ Saved batch 81 (50 quotations)
2025-07-10 20:02:23,379 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:23,380 - ERROR - Error saving batch 82: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:23,789 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:23,789 - ERROR - Error saving batch 83: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:24,099 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:24,108 - INFO - ✅ Saved batch 84 (50 quotations)
2025-07-10 20:02:24,506 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:24,509 - INFO - ✅ Saved batch 85 (50 quotations)
2025-07-10 20:02:24,817 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:24,818 - ERROR - Error saving batch 86: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:25,160 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:25,166 - INFO - ✅ Saved batch 87 (50 quotations)
2025-07-10 20:02:25,497 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:25,498 - ERROR - Error saving batch 88: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:25,790 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:25,797 - INFO - ✅ Saved batch 89 (50 quotations)
2025-07-10 20:02:26,132 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:26,136 - INFO - ✅ Saved batch 90 (50 quotations)
2025-07-10 20:02:26,453 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:26,456 - INFO - ✅ Saved batch 91 (50 quotations)
2025-07-10 20:02:26,776 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:26,778 - ERROR - Error saving batch 92: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:27,085 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:27,087 - INFO - ✅ Saved batch 93 (50 quotations)
2025-07-10 20:02:27,429 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:27,436 - INFO - ✅ Saved batch 94 (50 quotations)
2025-07-10 20:02:27,765 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:27,768 - INFO - ✅ Saved batch 95 (50 quotations)
2025-07-10 20:02:28,141 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:28,147 - INFO - ✅ Saved batch 96 (50 quotations)
2025-07-10 20:02:28,458 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:28,466 - INFO - ✅ Saved batch 97 (50 quotations)
2025-07-10 20:02:28,785 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:28,785 - ERROR - Error saving batch 98: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:29,115 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:29,118 - INFO - ✅ Saved batch 99 (50 quotations)
2025-07-10 20:02:29,525 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:29,528 - INFO - ✅ Saved batch 100 (50 quotations)
2025-07-10 20:02:29,829 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:29,836 - INFO - ✅ Saved batch 101 (50 quotations)
2025-07-10 20:02:30,119 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:30,125 - INFO - ✅ Saved batch 102 (50 quotations)
2025-07-10 20:02:30,449 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:30,457 - INFO - ✅ Saved batch 103 (50 quotations)
2025-07-10 20:02:30,772 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:30,774 - INFO - ✅ Saved batch 104 (50 quotations)
2025-07-10 20:02:31,085 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:31,087 - INFO - ✅ Saved batch 105 (50 quotations)
2025-07-10 20:02:31,469 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:31,470 - ERROR - Error saving batch 106: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:31,811 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:31,815 - INFO - ✅ Saved batch 107 (50 quotations)
2025-07-10 20:02:32,127 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:32,136 - INFO - ✅ Saved batch 108 (50 quotations)
2025-07-10 20:02:32,436 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:32,438 - ERROR - Error saving batch 109: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:32,745 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:32,748 - INFO - ✅ Saved batch 110 (50 quotations)
2025-07-10 20:02:33,067 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:33,076 - INFO - ✅ Saved batch 111 (50 quotations)
2025-07-10 20:02:33,414 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:33,415 - ERROR - Error saving batch 112: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:33,759 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:33,765 - INFO - ✅ Saved batch 113 (50 quotations)
2025-07-10 20:02:34,125 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:34,126 - ERROR - Error saving batch 114: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:34,543 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:34,545 - INFO - ✅ Saved batch 115 (50 quotations)
2025-07-10 20:02:34,897 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:34,906 - INFO - ✅ Saved batch 116 (50 quotations)
2025-07-10 20:02:35,186 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:35,190 - INFO - ✅ Saved batch 117 (50 quotations)
2025-07-10 20:02:35,566 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:35,569 - INFO - ✅ Saved batch 118 (50 quotations)
2025-07-10 20:02:35,882 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:35,886 - INFO - ✅ Saved batch 119 (50 quotations)
2025-07-10 20:02:36,215 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:36,227 - INFO - ✅ Saved batch 120 (50 quotations)
2025-07-10 20:02:36,535 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:36,548 - INFO - ✅ Saved batch 121 (50 quotations)
2025-07-10 20:02:36,899 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:36,902 - INFO - ✅ Saved batch 122 (50 quotations)
2025-07-10 20:02:37,205 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:37,207 - INFO - ✅ Saved batch 123 (50 quotations)
2025-07-10 20:02:37,614 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:37,617 - INFO - ✅ Saved batch 124 (50 quotations)
2025-07-10 20:02:37,932 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:37,937 - INFO - ✅ Saved batch 125 (50 quotations)
2025-07-10 20:02:38,252 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:38,256 - INFO - ✅ Saved batch 126 (50 quotations)
2025-07-10 20:02:38,600 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:38,612 - INFO - ✅ Saved batch 127 (50 quotations)
2025-07-10 20:02:39,006 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:39,008 - ERROR - Error saving batch 128: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:39,353 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:39,356 - INFO - ✅ Saved batch 129 (50 quotations)
2025-07-10 20:02:39,764 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:39,765 - ERROR - Error saving batch 130: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:40,065 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:40,067 - INFO - ✅ Saved batch 131 (50 quotations)
2025-07-10 20:02:40,481 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:40,483 - INFO - ✅ Saved batch 132 (50 quotations)
2025-07-10 20:02:40,891 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:40,893 - ERROR - Error saving batch 133: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:41,251 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:41,257 - INFO - ✅ Saved batch 134 (50 quotations)
2025-07-10 20:02:41,607 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:41,611 - INFO - ✅ Saved batch 135 (50 quotations)
2025-07-10 20:02:42,016 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:42,018 - INFO - ✅ Saved batch 136 (50 quotations)
2025-07-10 20:02:42,428 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:42,431 - INFO - ✅ Saved batch 137 (50 quotations)
2025-07-10 20:02:42,836 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:42,838 - INFO - ✅ Saved batch 138 (50 quotations)
2025-07-10 20:02:43,167 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:43,170 - INFO - ✅ Saved batch 139 (50 quotations)
2025-07-10 20:02:43,653 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:43,654 - ERROR - Error saving batch 140: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:43,998 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:44,009 - INFO - ✅ Saved batch 141 (50 quotations)
2025-07-10 20:02:44,334 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:44,336 - INFO - ✅ Saved batch 142 (50 quotations)
2025-07-10 20:02:44,642 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:44,649 - INFO - ✅ Saved batch 143 (50 quotations)
2025-07-10 20:02:45,205 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:45,206 - ERROR - Error saving batch 144: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:45,556 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:45,556 - ERROR - Error saving batch 145: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:45,908 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:45,911 - INFO - ✅ Saved batch 146 (50 quotations)
2025-07-10 20:02:46,319 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:46,321 - INFO - ✅ Saved batch 147 (50 quotations)
2025-07-10 20:02:46,726 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:46,728 - ERROR - Error saving batch 148: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:47,043 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:47,046 - INFO - ✅ Saved batch 149 (50 quotations)
2025-07-10 20:02:47,359 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:47,363 - INFO - ✅ Saved batch 150 (50 quotations)
2025-07-10 20:02:47,851 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:47,852 - ERROR - Error saving batch 151: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:48,263 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:48,264 - ERROR - Error saving batch 152: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:48,670 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:48,672 - INFO - ✅ Saved batch 153 (50 quotations)
2025-07-10 20:02:48,979 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:48,981 - ERROR - Error saving batch 154: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:49,286 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:49,288 - INFO - ✅ Saved batch 155 (50 quotations)
2025-07-10 20:02:49,574 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:49,579 - INFO - ✅ Saved batch 156 (50 quotations)
2025-07-10 20:02:49,919 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:49,930 - INFO - ✅ Saved batch 157 (50 quotations)
2025-07-10 20:02:50,226 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:50,227 - ERROR - Error saving batch 158: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:50,619 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:50,622 - INFO - ✅ Saved batch 159 (50 quotations)
2025-07-10 20:02:51,029 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:51,031 - INFO - ✅ Saved batch 160 (50 quotations)
2025-07-10 20:02:51,305 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:51,305 - ERROR - Error saving batch 161: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:51,607 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:51,616 - INFO - ✅ Saved batch 162 (50 quotations)
2025-07-10 20:02:51,950 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:51,952 - INFO - ✅ Saved batch 163 (50 quotations)
2025-07-10 20:02:52,267 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:52,270 - INFO - ✅ Saved batch 164 (50 quotations)
2025-07-10 20:02:52,618 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:52,619 - ERROR - Error saving batch 165: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:52,922 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:52,928 - INFO - ✅ Saved batch 166 (50 quotations)
2025-07-10 20:02:53,282 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:53,282 - ERROR - Error saving batch 167: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:53,573 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:53,578 - INFO - ✅ Saved batch 168 (50 quotations)
2025-07-10 20:02:53,997 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:53,998 - ERROR - Error saving batch 169: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:54,406 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:54,407 - ERROR - Error saving batch 170: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:54,764 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:54,767 - INFO - ✅ Saved batch 171 (50 quotations)
2025-07-10 20:02:55,080 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:55,086 - INFO - ✅ Saved batch 172 (50 quotations)
2025-07-10 20:02:55,431 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:55,434 - INFO - ✅ Saved batch 173 (50 quotations)
2025-07-10 20:02:55,769 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:55,776 - INFO - ✅ Saved batch 174 (50 quotations)
2025-07-10 20:02:56,083 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:56,087 - INFO - ✅ Saved batch 175 (50 quotations)
2025-07-10 20:02:56,455 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:56,456 - ERROR - Error saving batch 176: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:56,759 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:56,766 - INFO - ✅ Saved batch 177 (50 quotations)
2025-07-10 20:02:57,065 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:57,066 - ERROR - Error saving batch 178: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:57,371 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:57,376 - INFO - ✅ Saved batch 179 (50 quotations)
2025-07-10 20:02:57,744 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:57,748 - INFO - ✅ Saved batch 180 (50 quotations)
2025-07-10 20:02:58,100 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:58,108 - INFO - ✅ Saved batch 181 (50 quotations)
2025-07-10 20:02:58,504 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:58,507 - INFO - ✅ Saved batch 182 (50 quotations)
2025-07-10 20:02:58,825 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:02:58,827 - INFO - ✅ Saved batch 183 (50 quotations)
2025-07-10 20:02:59,177 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:59,179 - ERROR - Error saving batch 184: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:59,487 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:59,488 - ERROR - Error saving batch 185: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:02:59,815 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:02:59,816 - ERROR - Error saving batch 186: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:03:00,141 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:03:00,148 - INFO - ✅ Saved batch 187 (50 quotations)
2025-07-10 20:03:00,437 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:03:00,440 - INFO - ✅ Saved batch 188 (50 quotations)
2025-07-10 20:03:00,758 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:03:00,761 - INFO - ✅ Saved batch 189 (50 quotations)
2025-07-10 20:03:01,056 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:03:01,058 - ERROR - Error saving batch 190: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:03:01,471 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:03:01,472 - ERROR - Error saving batch 191: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:03:01,841 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:03:01,842 - INFO - ✅ Saved batch 192 (50 quotations)
2025-07-10 20:03:02,153 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:03:02,156 - INFO - ✅ Saved batch 193 (50 quotations)
2025-07-10 20:03:02,451 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:03:02,455 - INFO - ✅ Saved batch 194 (50 quotations)
2025-07-10 20:03:02,793 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:03:02,798 - INFO - ✅ Saved batch 195 (50 quotations)
2025-07-10 20:03:03,096 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 500 Internal Server Error"
2025-07-10 20:03:03,098 - ERROR - Error saving batch 196: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}
2025-07-10 20:03:03,366 - INFO - HTTP Request: POST https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/quotations?on_conflict=quotation_number&columns=%22closing_date%22%2C%22invitation_links%22%2C%22scraped_at%22%2C%22quotation_number%22%2C%22closing_date_time%22%2C%22submission_at%22%2C%22status%22%2C%22description%22%2C%22run_id%22 "HTTP/2 201 Created"
2025-07-10 20:03:03,367 - INFO - ✅ Saved batch 197 (26 quotations)
2025-07-10 20:03:03,367 - INFO - ✅ Successfully saved 7026/9826 quotations to Supabase
2025-07-10 20:03:03,704 - INFO - HTTP Request: PATCH https://uvksgkpxeyyssvdsxbts.supabase.co/rest/v1/scraping_runs?run_id=eq.72648412-72db-4044-8c90-a4c9238a2cad "HTTP/2 400 Bad Request"
2025-07-10 20:03:03,705 - ERROR - Failed to update scraping run: {'message': "Could not find the 'new_quotations_added' column of 'scraping_runs' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-07-10 20:03:03,705 - INFO - 
🎯 Scraping completed in 102.80 seconds
Run ID: 72648412-72db-4044-8c90-a4c9238a2cad
Status: partial
Total Quotations: 9826
New Quotations: 7026
Errors: 56
            
2025-07-10 20:03:03,705 - WARNING - Errors encountered: ["Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}", "Batch save error: {'message': 'ON CONFLICT DO UPDATE command cannot affect row a second time', 'code': '21000', 'hint': 'Ensure that no rows proposed for insertion within the same command have duplicate constrained values.', 'details': None}"]
