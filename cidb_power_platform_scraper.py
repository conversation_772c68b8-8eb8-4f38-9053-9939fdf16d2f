#!/usr/bin/env python3
"""
CIDB Power Platform Portal Scraper
Scrapes contractor data from Microsoft Power Platform portal
Uses browser automation to interact with the data grid
"""

import requests
import time
import json
import logging
from datetime import datetime
import pandas as pd
from bs4 import BeautifulSoup
import re

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('cidb_power_platform_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CIDBPowerPlatformScraper:
    def __init__(self):
        self.base_url = "https://portal.cidb.org.za"
        self.contractor_url = "https://portal.cidb.org.za/RegisterOfContractors/"
        self.session = requests.Session()
        
        # Headers to mimic a real browser
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1"
        }
        
        self.session.headers.update(self.headers)
    
    def get_portal_token(self):
        """Get authentication token from Power Platform portal"""
        try:
            logger.info("Getting portal authentication token...")
            response = self.session.get(self.contractor_url, timeout=30)
            response.raise_for_status()
            
            # Look for any tokens or session data in the response
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Check for any hidden form fields that might contain tokens
            hidden_inputs = soup.find_all('input', type='hidden')
            tokens = {}
            
            for input_field in hidden_inputs:
                name = input_field.get('name', '')
                value = input_field.get('value', '')
                if any(keyword in name.lower() for keyword in ['token', 'state', 'session', 'auth']):
                    tokens[name] = value
                    logger.info(f"Found token field: {name}")
            
            return tokens
            
        except Exception as e:
            logger.error(f"Error getting portal token: {e}")
            return {}
    
    def discover_api_endpoints(self):
        """Discover Power Platform API endpoints"""
        try:
            logger.info("Discovering Power Platform API endpoints...")
            
            # Common Power Platform portal endpoints
            potential_endpoints = [
                "/_api/",
                "/_odata/",
                "/_services/",
                "/api/data/v9.0/",
                "/api/data/v9.1/",
                "/api/data/v9.2/",
                "/_portal/",
                "/webapi/",
                "/XRMServices/2011/OrganizationData.svc/"
            ]
            
            working_endpoints = []
            
            for endpoint in potential_endpoints:
                url = self.base_url + endpoint
                try:
                    response = self.session.get(url, timeout=10)
                    if response.status_code in [200, 401, 403]:  # 401/403 means endpoint exists but needs auth
                        working_endpoints.append(endpoint)
                        logger.info(f"Found potential endpoint: {endpoint} (Status: {response.status_code})")
                        
                        # Try to get metadata
                        if response.status_code == 200:
                            content_preview = response.text[:200]
                            logger.info(f"Content preview: {content_preview}")
                            
                except Exception as e:
                    logger.debug(f"Endpoint {endpoint} not accessible: {e}")
            
            return working_endpoints
            
        except Exception as e:
            logger.error(f"Error discovering endpoints: {e}")
            return []
    
    def try_odata_queries(self):
        """Try various OData queries to find contractor data"""
        try:
            logger.info("Trying OData queries for contractor data...")
            
            # Common entity names for contractors
            entity_names = [
                "contractors",
                "cidb_contractors", 
                "contractor",
                "cidb_contractor",
                "accounts",
                "contacts",
                "leads",
                "opportunities"
            ]
            
            odata_endpoints = [
                "/_api/",
                "/_odata/",
                "/api/data/v9.2/",
                "/webapi/"
            ]
            
            for endpoint in odata_endpoints:
                for entity in entity_names:
                    url = f"{self.base_url}{endpoint}{entity}"
                    
                    try:
                        response = self.session.get(url, timeout=10)
                        logger.info(f"Trying {url}: Status {response.status_code}")
                        
                        if response.status_code == 200:
                            logger.info(f"SUCCESS! Found working OData endpoint: {url}")
                            
                            # Try to parse the response
                            try:
                                data = response.json()
                                if 'value' in data and len(data['value']) > 0:
                                    logger.info(f"Found {len(data['value'])} records")
                                    logger.info(f"Sample record keys: {list(data['value'][0].keys())}")
                                    return url, data
                            except:
                                logger.info(f"Response is not JSON, content preview: {response.text[:200]}")
                                
                    except Exception as e:
                        logger.debug(f"Error querying {url}: {e}")
            
            return None, None
            
        except Exception as e:
            logger.error(f"Error trying OData queries: {e}")
            return None, None
    
    def scrape_with_selenium_fallback(self):
        """Fallback to browser automation if API methods fail"""
        logger.info("API methods failed, would need browser automation...")
        logger.info("This would require Selenium or Playwright to:")
        logger.info("1. Load the page")
        logger.info("2. Wait for the data grid to load")
        logger.info("3. Extract data from the grid")
        logger.info("4. Handle pagination")
        
        # For now, return empty data
        return []
    
    def run_discovery(self):
        """Run the complete discovery process"""
        logger.info("Starting CIDB Power Platform discovery...")
        
        # Step 1: Get portal tokens
        tokens = self.get_portal_token()
        
        # Step 2: Discover API endpoints
        endpoints = self.discover_api_endpoints()
        
        # Step 3: Try OData queries
        working_url, data = self.try_odata_queries()
        
        if working_url and data:
            logger.info(f"Successfully found contractor data at: {working_url}")
            return data
        else:
            logger.warning("No working API endpoints found")
            logger.info("The CIDB portal uses a Power Platform data grid that loads data dynamically")
            logger.info("Browser automation would be required to extract the data")
            return None

def main():
    """Main execution function"""
    scraper = CIDBPowerPlatformScraper()
    
    try:
        result = scraper.run_discovery()
        
        if result:
            # Save the discovered data
            with open('cidb_discovered_data.json', 'w') as f:
                json.dump(result, f, indent=2)
            logger.info("Saved discovered data to cidb_discovered_data.json")
            
            # Try to convert to CSV if we have contractor-like data
            if 'value' in result and len(result['value']) > 0:
                df = pd.DataFrame(result['value'])
                df.to_csv('cidb_discovered_contractors.csv', index=False)
                logger.info("Saved contractor data to cidb_discovered_contractors.csv")
        else:
            logger.info("No data discovered through API methods")
            logger.info("Browser automation would be required for this Power Platform portal")
            
    except Exception as e:
        logger.error(f"Error during discovery: {e}")

if __name__ == "__main__":
    main()
