#!/usr/bin/env python3
"""
CIDB Direct API Scraper
Advanced scraping using direct API calls to CIDB backend
Much faster and more reliable than browser automation
"""

import requests
import pandas as pd
import time
from tqdm import tqdm
import logging
from datetime import datetime
import json

# --- Configuration ---
API_URL = "https://portal.cidb.org.za/RegisterOfContractors/GetContractorByCriteria"
CSV_FILENAME = "cidb_contractor_data.csv"
PAGE_SIZE = 100  # Number of records to fetch per API call (higher is faster)

# Mimic a real browser's headers to avoid being blocked
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "X-Requested-With": "XMLHttpRequest",
    "Referer": "https://portal.cidb.org.za/RegisterOfContractors/",
    "Accept": "application/json, text/javascript, */*; q=0.01",
    "Accept-Language": "en-US,en;q=0.9",
    "Accept-Encoding": "gzip, deflate, br",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"
}

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('cidb_direct_api_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_total_records(session, is_active_status):
    """
    Performs an initial request to find the total number of records
    to set up the progress bar.
    """
    payload = {
        "draw": 1,
        "start": 0,
        "length": 1,  # Only need 1 record to get the total
        "search[value]": "",
        "search[regex]": "false",
        "IsActive": str(is_active_status).lower(),
    }
    
    try:
        logger.info(f"Getting total record count for {'Active' if is_active_status else 'Inactive'} contractors...")
        response = session.post(API_URL, data=payload, headers=HEADERS, timeout=30)
        response.raise_for_status()
        data = response.json()
        total = data.get("recordsFiltered", 0)
        logger.info(f"Found {total} total records")
        return total
    except requests.RequestException as e:
        logger.error(f"Error getting total record count: {e}")
        return 0
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing JSON response: {e}")
        logger.error(f"Response content: {response.text[:500]}...")
        return 0

def scrape_cidb_data(session, is_active_status):
    """
    Scrapes all contractor data for a given status (Active/Inactive).
    
    Args:
        session (requests.Session): The session object to make requests.
        is_active_status (bool): True to scrape Active, False for Inactive.
        
    Returns:
        list: A list of dictionaries containing contractor data.
    """
    status_string = "Active" if is_active_status else "Inactive"
    logger.info(f"Starting scrape for {status_string} contractors")
    
    total_records = get_total_records(session, is_active_status)
    if total_records == 0:
        logger.warning(f"No records found for {status_string} contractors.")
        return []

    all_contractors = []
    start_index = 0
    
    with tqdm(total=total_records, desc=f"Scraping {status_string}") as pbar:
        while start_index < total_records:
            payload = {
                "draw": 1,
                "start": start_index,
                "length": PAGE_SIZE,
                "search[value]": "",
                "search[regex]": "false",
                "IsActive": str(is_active_status).lower(),
            }
            
            try:
                logger.debug(f"Requesting records {start_index} to {start_index + PAGE_SIZE}")
                response = session.post(API_URL, data=payload, headers=HEADERS, timeout=30)
                response.raise_for_status()
                
                data = response.json()
                
                contractor_batch = data.get("data", [])
                if not contractor_batch:
                    logger.info("No more data available, breaking loop")
                    break
                
                # Add status information to each contractor
                for contractor in contractor_batch:
                    contractor['contractor_status'] = status_string
                    contractor['scraped_at'] = datetime.utcnow().isoformat()
                
                all_contractors.extend(contractor_batch)
                
                pbar.update(len(contractor_batch))
                start_index += PAGE_SIZE
                
                logger.info(f"Retrieved {len(contractor_batch)} contractors (Total: {len(all_contractors)})")
                
                # Be polite to the server
                time.sleep(0.5)

            except requests.exceptions.RequestException as e:
                logger.error(f"Request error: {e}")
                logger.info("Pausing for 10 seconds before retrying...")
                time.sleep(10)
            except json.JSONDecodeError as e:
                logger.error(f"JSON decode error: {e}")
                logger.error(f"Response text: {response.text[:200]}...")
                time.sleep(10)
            except Exception as e:
                logger.error(f"Unexpected error: {e}")
                time.sleep(5)
                
    logger.info(f"Completed scraping {status_string} contractors: {len(all_contractors)} total")
    return all_contractors

def process_and_save_data(all_data):
    """
    Processes the raw scraped data, sorts it, and saves it to a CSV file.
    """
    if not all_data:
        logger.warning("No data was scraped. Exiting without creating a file.")
        return

    logger.info("Processing and sorting all collected data...")
    
    # The API returns data with numeric keys. Map them to readable column names.
    column_mapping = {
        '0': 'CRS Number',
        '1': 'Contractor Name',
        '2': 'Status',
        '3': 'Grading',
        '4': 'Expiry Date',
        '5': 'Trading Name',
        '6': 'Province',
        '7': 'City',
        '8': 'B-BBEE Status',
        '9': 'Potentially Emerging'
    }

    df = pd.DataFrame(all_data)
    
    # Check if we have the expected columns
    available_columns = [col for col in column_mapping.keys() if col in df.columns]
    if not available_columns:
        logger.error("No expected columns found in data. Saving raw data...")
        df.to_csv(f"raw_{CSV_FILENAME}", index=False, encoding='utf-8-sig')
        return
    
    df = df[available_columns].rename(columns=column_mapping)

    # Convert columns to appropriate types for correct sorting
    if 'CRS Number' in df.columns:
        df['CRS Number'] = pd.to_numeric(df['CRS Number'], errors='coerce')
    if 'Expiry Date' in df.columns:
        df['Expiry Date'] = pd.to_datetime(df['Expiry Date'], errors='coerce')

    logger.info("Applying custom sorting rules...")
    
    # Sort according to user specifications
    sort_columns = []
    sort_ascending = []
    
    for col, ascending in [
        ('CRS Number', False),
        ('Contractor Name', True),
        ('Status', False),
        ('Grading', False),
        ('Expiry Date', False),
        ('Trading Name', False),
        ('Province', False),
        ('City', False),
        ('B-BBEE Status', False),
        ('Potentially Emerging', False)
    ]:
        if col in df.columns:
            sort_columns.append(col)
            sort_ascending.append(ascending)
    
    if sort_columns:
        df_sorted = df.sort_values(by=sort_columns, ascending=sort_ascending)
    else:
        df_sorted = df
    
    # Save to CSV
    try:
        df_sorted.to_csv(CSV_FILENAME, index=False, encoding='utf-8-sig')
        logger.info(f"✅ Success! Data scraped and saved to '{CSV_FILENAME}'")
        logger.info(f"Total records processed: {len(df_sorted)}")
        
        # Print summary statistics
        if 'contractor_status' in df_sorted.columns:
            status_counts = df_sorted['contractor_status'].value_counts()
            logger.info(f"Status breakdown: {status_counts.to_dict()}")
            
    except Exception as e:
        logger.error(f"Error saving data to CSV: {e}")

def main():
    """Main execution function"""
    logger.info("Starting CIDB Direct API Scraper")
    logger.info(f"Target API: {API_URL}")
    logger.info(f"Output file: {CSV_FILENAME}")
    
    start_time = datetime.utcnow()
    
    # Use a session object to persist connections
    with requests.Session() as session:
        # 1. Scrape Active Contractors
        logger.info("=" * 50)
        active_data = scrape_cidb_data(session, is_active_status=True)
        
        # 2. Scrape Inactive Contractors  
        logger.info("=" * 50)
        inactive_data = scrape_cidb_data(session, is_active_status=False)
        
    # 3. Combine and process the data
    logger.info("=" * 50)
    combined_data = active_data + inactive_data
    process_and_save_data(combined_data)
    
    end_time = datetime.utcnow()
    duration = (end_time - start_time).total_seconds()
    
    logger.info("=" * 50)
    logger.info(f"Scraping completed in {duration:.2f} seconds")
    logger.info(f"Total contractors scraped: {len(combined_data)}")
    logger.info(f"Active contractors: {len(active_data)}")
    logger.info(f"Inactive contractors: {len(inactive_data)}")

if __name__ == "__main__":
    main()
